"""
Debug script to examine the actual HTML structure of the forum
"""

import time
import logging
from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_forum_structure():
    """Debug forum structure to find correct selectors"""
    logger.info("Starting forum structure analysis...")
    
    try:
        # Configure DrissionPage
        Settings.set_singleton_tab_obj(False)
        
        # Create browser
        browser = Chromium()
        page = browser.new_tab()
        
        # Navigate to forum
        url = "https://lowendtalk.com/categories/offers"
        logger.info(f"Navigating to: {url}")
        page.get(url)
        time.sleep(5)
        
        # Find the main container
        container = page.ele('tag:ul@class=DataList Discussions', timeout=10)
        if container:
            logger.info("✅ Found main container")
            
            # Get all direct children of the container
            children = container.eles('xpath:./li')
            logger.info(f"Found {len(children)} direct li children")
            
            # Examine the first few children
            for i, child in enumerate(children[:3]):
                logger.info(f"\n--- Examining child {i+1} ---")
                
                # Get class attribute
                try:
                    class_attr = child.attr('class')
                    logger.info(f"Classes: {class_attr}")
                except Exception as e:
                    logger.warning(f"Could not get class: {e}")
                
                # Get id attribute
                try:
                    id_attr = child.attr('id')
                    if id_attr:
                        logger.info(f"ID: {id_attr}")
                except Exception as e:
                    logger.warning(f"Could not get id: {e}")
                
                # Look for title elements
                title_selectors = ['.Title', '.ItemContent .Title', 'h3', 'a']
                for selector in title_selectors:
                    try:
                        title_elem = child.ele(selector)
                        if title_elem:
                            title_text = title_elem.text
                            if title_text and len(title_text.strip()) > 5:
                                logger.info(f"Title (via {selector}): {title_text[:60]}...")
                                break
                    except Exception:
                        continue
                
                # Look for common forum elements
                common_selectors = ['.ItemContent', '.Meta', '.Discussion', '.DiscussionName']
                for selector in common_selectors:
                    try:
                        elem = child.ele(selector)
                        if elem:
                            logger.info(f"Found element: {selector}")
                    except Exception:
                        continue
            
            # Try to find all possible discussion-related elements
            logger.info("\n--- Looking for discussion elements in container ---")
            discussion_selectors = [
                'li',
                '[class*="Discussion"]',
                '[class*="Item"]',
                '[class*="discussion"]',
                '[class*="item"]'
            ]
            
            for selector in discussion_selectors:
                try:
                    elements = container.eles(selector)
                    if elements:
                        logger.info(f"Found {len(elements)} elements with selector: {selector}")
                        
                        # Check first element's classes
                        if elements:
                            first_elem = elements[0]
                            try:
                                classes = first_elem.attr('class')
                                logger.info(f"  First element classes: {classes}")
                            except Exception:
                                pass
                except Exception as e:
                    logger.warning(f"Error with selector {selector}: {e}")
        
        else:
            logger.error("❌ Could not find main container")
        
        logger.info("Structure analysis completed")
        
    except Exception as e:
        logger.error(f"Critical error in structure analysis: {e}")
    finally:
        try:
            if 'browser' in locals():
                browser.quit()
        except Exception as e:
            logger.error(f"Error closing browser: {e}")

if __name__ == "__main__":
    debug_forum_structure()
