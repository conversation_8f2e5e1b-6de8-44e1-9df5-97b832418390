"""
Quick fix version of the forum crawler with correct selectors
"""

import time
import logging
import json
import threading
import queue
from datetime import datetime, timezone
from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables
task_queue = queue.Queue()
results = []
results_lock = threading.Lock()
monitor_ready = threading.Event()

def extract_post_data(item):
    """Extract post data with correct selectors"""
    try:
        # Try to find title link directly
        title_link = item.ele('a', timeout=2)
        if not title_link:
            logger.debug("No title link found")
            return None
        
        title = title_link.text.strip()
        url = title_link.link
        
        if not title or not url:
            logger.debug("Missing title or URL")
            return None
        
        # Ensure absolute URL
        if not url.startswith('http'):
            url = 'https://lowendtalk.com' + url
        
        # Try to extract comment count
        comment_count = 0
        try:
            # Look for comment count in various places
            count_elements = item.eles('.Number')
            for elem in count_elements:
                text = elem.text.strip()
                if text.isdigit():
                    comment_count = int(text)
                    break
                elif 'k' in text.lower():
                    try:
                        num = float(text.lower().replace('k', ''))
                        comment_count = int(num * 1000)
                        break
                    except:
                        pass
        except Exception as e:
            logger.debug(f"Could not extract comment count: {e}")
        
        logger.info(f"Extracted: {title[:50]}... (Comments: {comment_count})")
        
        return {
            "post_url": url,
            "post_title": title,
            "current_comment_count": comment_count,
            "datetime_attr": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error extracting post data: {e}")
        return None

def monitor_thread():
    """Monitor thread function"""
    logger.info("Starting monitor thread...")
    
    try:
        Settings.set_singleton_tab_obj(False)
        browser = Chromium()
        page = browser.new_tab()
        
        # Navigate to forum
        url = "https://lowendtalk.com/categories/offers"
        logger.info(f"Loading: {url}")
        page.get(url)
        time.sleep(3)
        
        # Find posts container
        container = page.ele('tag:ul@class=DataList Discussions', timeout=10)
        if not container:
            logger.error("Could not find posts container")
            return
        
        # Get posts
        posts = container.eles('xpath:./li[contains(@class, "Item")]')
        logger.info(f"Found {len(posts)} posts")
        
        # Process first 3 posts for testing
        posts_to_process = posts[:3]
        
        for i, post in enumerate(posts_to_process):
            logger.info(f"Processing post {i+1}/{len(posts_to_process)}")
            
            post_data = extract_post_data(post)
            if post_data:
                task_queue.put(post_data)
                logger.info(f"Queued: {post_data['post_title'][:50]}...")
            else:
                logger.warning(f"Could not extract data from post {i+1}")
        
        # Signal that monitor is ready
        monitor_ready.set()
        logger.info("Monitor thread completed, workers can start")
        
        # Keep browser open for workers
        time.sleep(300)  # Keep running for 5 minutes
        
    except Exception as e:
        logger.error(f"Monitor thread error: {e}")
    finally:
        monitor_ready.set()  # Always set the event
        try:
            browser.quit()
        except:
            pass

def worker_thread(worker_id):
    """Worker thread function"""
    logger.info(f"Worker {worker_id} starting...")
    
    # Wait for monitor to be ready
    if not monitor_ready.wait(timeout=60):
        logger.warning(f"Worker {worker_id}: Monitor not ready, exiting")
        return
    
    try:
        Settings.set_singleton_tab_obj(False)
        browser = Chromium()
        page = browser.new_tab()
        
        while True:
            try:
                # Get task from queue
                task_data = task_queue.get(timeout=10)
                
                post_url = task_data["post_url"]
                post_title = task_data["post_title"]
                
                logger.info(f"Worker {worker_id}: Processing {post_title[:50]}...")
                
                # Navigate to post
                page.get(post_url)
                time.sleep(2)
                
                # Scroll to load comments
                page.scroll.to_bottom()
                time.sleep(2)
                
                # Get comments
                comments = page.eles('.Comment')
                logger.info(f"Worker {worker_id}: Found {len(comments)} comments")
                
                # Check for flash sales in comments
                flash_sales_found = []
                keywords = ['sale', 'offer', 'discount', 'promo', '$', 'gb', 'vps']
                
                for j, comment in enumerate(comments[:10]):  # Check first 10 comments
                    try:
                        comment_text = comment.text.strip()
                        if len(comment_text) > 20:  # Skip very short comments
                            # Simple keyword check
                            text_lower = comment_text.lower()
                            keyword_count = sum(1 for keyword in keywords if keyword in text_lower)
                            
                            if keyword_count >= 2:  # At least 2 keywords
                                flash_sale = {
                                    "post_title": post_title,
                                    "post_url": post_url,
                                    "comment_id": f"comment_{j}",
                                    "comment_text": comment_text[:200] + "..." if len(comment_text) > 200 else comment_text,
                                    "keywords_found": keyword_count,
                                    "crawled_time": datetime.now(timezone.utc).isoformat()
                                }
                                flash_sales_found.append(flash_sale)
                                logger.info(f"Worker {worker_id}: 🎉 Flash sale detected! Keywords: {keyword_count}")
                    except Exception as e:
                        logger.debug(f"Error processing comment {j}: {e}")
                
                # Save results
                if flash_sales_found:
                    with results_lock:
                        results.extend(flash_sales_found)
                        # Save to file
                        with open('lowendtalk_flash_sales.json', 'w', encoding='utf-8') as f:
                            json.dump(results, f, ensure_ascii=False, indent=2)
                        logger.info(f"Worker {worker_id}: Saved {len(flash_sales_found)} flash sales to file")
                
                task_queue.task_done()
                
            except queue.Empty:
                logger.info(f"Worker {worker_id}: No more tasks, exiting")
                break
            except Exception as e:
                logger.error(f"Worker {worker_id}: Error processing task: {e}")
                task_queue.task_done()
                
    except Exception as e:
        logger.error(f"Worker {worker_id}: Critical error: {e}")
    finally:
        try:
            browser.quit()
        except:
            pass
        logger.info(f"Worker {worker_id}: Ended")

def main():
    """Main function"""
    logger.info("Starting Quick Fix Forum Crawler...")
    
    # Start monitor thread
    monitor = threading.Thread(target=monitor_thread, name="Monitor", daemon=True)
    monitor.start()
    
    # Start 2 worker threads
    workers = []
    for i in range(2):
        worker = threading.Thread(target=worker_thread, args=(i+1,), name=f"Worker-{i+1}", daemon=True)
        worker.start()
        workers.append(worker)
    
    # Wait for threads to complete
    try:
        monitor.join(timeout=120)  # Wait up to 2 minutes for monitor
        
        for worker in workers:
            worker.join(timeout=60)  # Wait up to 1 minute for each worker
        
        # Final results summary
        logger.info(f"Crawling completed. Found {len(results)} flash sales total.")
        
        if results:
            logger.info("Flash sales found:")
            for i, sale in enumerate(results[:3]):  # Show first 3
                logger.info(f"  {i+1}. {sale['post_title'][:50]}... (Keywords: {sale['keywords_found']})")
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Main error: {e}")

if __name__ == "__main__":
    main()
