import json
import re
import time
import os
import threading
import queue
from datetime import datetime, timedelta, timezone
from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# --- 配置参数 ---
LOWENDTALK_BASE_URL = "https://lowendtalk.com/"
LOWENDTALK_MONITOR_URL = "https://lowendtalk.com/categories/offers"  # 建议使用 Offers 版块
STATE_FILE = "lowendtalk_crawl_state.json"
RESULTS_FILE = "lowendtalk_flash_sales.json"

# 闪购识别关键词 (可以根据实际情况添加更多)
FLASH_SALE_KEYWORDS = [
    "offer", "sale", "discount", "promo", "limited", "flash sale",
    "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
    "$/month", "$/yr", "$/year", "$/mo", "price", "deal"
]

# --- 共享资源 ---
task_queue = queue.Queue()  # 任务队列，存放待处理的帖子详情页URL
# 锁用于保护对共享状态 (processed_posts) 和结果 (all_flash_sales_found_this_run) 的访问
shared_data_lock = threading.Lock()
all_flash_sales_found_this_run = []  # 本次运行发现的闪购信息，由所有工作线程共同写入

# 事件：用于信号通知监控线程已完成首次任务提交
monitor_ready_event = threading.Event()


# --- 辅助函数 ---

def load_state():
    """加载之前保存的爬取状态"""
    with shared_data_lock:  # 确保文件操作在锁保护下进行
        try:
            if os.path.exists(STATE_FILE):
                with open(STATE_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"processed_posts": {}}
        except Exception as e:
            print(f"⚠️ 加载状态文件失败: {e}")
            return {"processed_posts": {}}


def save_state(state):
    """保存当前爬取状态"""
    with shared_data_lock:  # 确保文件操作在锁保护下进行
        try:
            with open(STATE_FILE, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"⚠️ 保存状态文件失败: {e}")


def load_results():
    """加载之前发现的闪购信息"""
    with shared_data_lock:  # 确保文件操作在锁保护下进行
        try:
            if os.path.exists(RESULTS_FILE):
                with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"⚠️ 加载结果文件失败: {e}")
            return []


def save_results(results_to_add):
    """保存发现的闪购信息 (增量追加并去重)"""
    with shared_data_lock:  # 确保文件操作在锁保护下进行
        existing_results = load_results()  # 重新加载以获取最新状态

        # 使用一个集合来存储现有结果的哈希值，便于快速去重
        existing_hashes = {hash(json.dumps(item, sort_keys=True)) for item in existing_results}

        new_items_added = 0
        for new_item in results_to_add:
            item_hash = hash(json.dumps(new_item, sort_keys=True))
            if item_hash not in existing_hashes:
                existing_results.append(new_item)
                existing_hashes.add(item_hash)
                new_items_added += 1

        if new_items_added > 0:
            try:
                with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(existing_results, f, ensure_ascii=False, indent=4)
                print(f"✅ 已保存 {new_items_added} 条新的闪购信息到 {RESULTS_FILE}")
            except Exception as e:
                print(f"⚠️ 保存新的闪购信息到文件失败: {e}")
        else:
            print("ℹ️ 未发现新的闪购信息需要保存。")


def is_flash_sale(text):
    """简单判断文本是否包含闪购关键词"""
    text_lower = text.lower()
    for keyword in FLASH_SALE_KEYWORDS:
        if keyword in text_lower:
            return True
    return False


def parse_time_string(time_str):
    """
    解析时间字符串为 timezone-aware datetime 对象。
    处理 ISO 格式 (如 '2025-07-14T04:04:41+00:00')。
    如果 time_str 为 None 或无法解析，返回 None。
    """
    if time_str is None:
        return None
    try:
        dt_obj = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        # 如果解析出的 datetime 对象是 naive (无时区信息)，则假定它是 UTC
        if dt_obj.tzinfo is None:
            return dt_obj.replace(tzinfo=timezone.utc)
        return dt_obj
    except ValueError:
        # 尝试处理 'July 13' 这种格式，需要补充年份
        current_year = datetime.now().year
        try:
            # 尝试解析 'Month Day, Year'
            return datetime.strptime(f"{time_str}, {current_year}", "%B %d, %Y").replace(tzinfo=timezone.utc)
        except ValueError:
            # 尝试解析 'Month Day Year' (如果时间字符串是来自 title 属性)
            try:
                return datetime.strptime(f"{time_str} {current_year}", "%B %d %Y").replace(tzinfo=timezone.utc)
            except ValueError:
                return None  # 无法解析


# --- 工作线程函数 ---
def worker_thread_func(worker_id, browser_instance):
    """
    工作线程函数，负责从队列中获取任务并处理帖子详情页。
    每个工作线程使用同一个浏览器实例下的一个新标签页。
    """
    print(f"[Worker-{worker_id}] 启动工作线程...")
    worker_page = None
    try:
        # 在同一个浏览器实例中创建新的标签页
        worker_page = browser_instance.new_tab()

        # 等待监控线程完成首次任务提交
        print(f"[Worker-{worker_id}] 等待监控线程就绪...")
        # 设置一个较长的等待超时时间，以确保监控线程有足够时间加载页面和提交初始任务
        if not monitor_ready_event.wait(timeout=90):  # 等待最多90秒
            print(f"[Worker-{worker_id}] ⚠️ 监控线程在超时时间内未就绪，工作线程退出。")
            return  # 如果超时还没就绪，则工作线程直接退出

        while True:
            try:
                # 从任务队列获取任务，设置超时以允许线程在队列为空时退出
                # 增加超时时间，给监控线程更多时间填充队列
                task_data = task_queue.get(timeout=30)  # 等待30秒，如果无任务则退出

                post_url = task_data["post_url"]
                post_title = task_data["post_title"]
                current_comment_count = task_data["current_comment_count"]
                datetime_attr = task_data["datetime_attr"]  # 原始 UTC 时间字符串

                print(f"[Worker-{worker_id}] 正在处理帖子: {post_title} (URL: {post_url})")

                # --- 进入帖子详情页抓取评论 ---
                worker_page.get(post_url)
                worker_page.wait.load_start()

                # 滚动到页面底部以加载所有评论
                last_height = worker_page.scroll.to_bottom()
                while True:
                    time.sleep(2)  # 等待加载
                    new_height = worker_page.scroll.to_bottom()
                    if new_height == last_height:
                        break  # 页面不再滚动，认为已加载所有评论
                    last_height = new_height

                # 获取所有评论元素
                comments = worker_page.eles('.Comment')
                print(f"[Worker-{worker_id}] 成功抓取到 {len(comments)} 条评论。")

                # 从共享状态中获取该帖子之前已处理的评论ID
                with shared_data_lock:
                    current_state = load_state()  # 重新加载以获取最新状态
                    processed_posts_in_state = current_state.get("processed_posts", {})
                    prev_processed_comment_ids = set(
                        processed_posts_in_state.get(post_url, {}).get("processed_comment_ids", []))

                current_post_processed_comment_ids = set()  # 本次运行中此工作线程处理的评论ID

                for comment_element in comments:
                    comment_id_str = comment_element.attr('id')
                    if not comment_id_str:
                        continue

                    # 如果评论ID已在之前的运行中处理过，则跳过
                    if comment_id_str in prev_processed_comment_ids:
                        continue

                    # 如果评论ID已在本次运行中被当前工作线程处理过，则跳过
                    if comment_id_str in current_post_processed_comment_ids:
                        continue

                    comment_text_element = comment_element.ele('.Message')
                    if not comment_text_element:
                        continue

                    comment_text = comment_text_element.text.strip()

                    # 检查是否包含闪购信息
                    if is_flash_sale(comment_text):
                        flash_sale_info = {
                            "post_title": post_title,
                            "post_url": post_url,
                            "comment_id": comment_id_str,
                            "comment_text": comment_text,
                            "crawled_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                        }
                        # 将发现的闪购信息添加到共享结果列表
                        with shared_data_lock:
                            all_flash_sales_found_this_run.append(flash_sale_info)
                        print(f"[Worker-{worker_id}] 🎉 发现闪购信息！评论ID: {comment_id_str}")
                        print(f"[Worker-{worker_id}]    内容: {comment_text[:100]}...")

                    current_post_processed_comment_ids.add(comment_id_str)

                # 更新帖子的状态：保存新的评论数和最新回复时间，以及所有已处理的评论ID
                with shared_data_lock:
                    current_state = load_state()  # 再次加载以确保获取最新全局状态
                    processed_posts_in_state = current_state.get("processed_posts", {})

                    # 合并新处理的评论ID与已存在的ID
                    existing_ids_for_post = set(
                        processed_posts_in_state.get(post_url, {}).get("processed_comment_ids", []))
                    updated_processed_ids = list(existing_ids_for_post.union(current_post_processed_comment_ids))

                    processed_posts_in_state[post_url] = {
                        "last_comment_count": current_comment_count,
                        "last_comment_datetime": datetime_attr,  # 保存原始 UTC 字符串
                        "processed_comment_ids": updated_processed_ids
                    }
                    current_state["processed_posts"] = processed_posts_in_state
                    save_state(current_state)  # 保存更新后的状态
                    print(f"[Worker-{worker_id}] 帖子 '{post_title}' 状态已更新。")

                task_queue.task_done()  # 标记任务完成

            except queue.Empty:
                # 队列为空，表示没有更多任务，工作线程退出
                print(f"[Worker-{worker_id}] 任务队列为空，工作线程退出。")
                break
            except Exception as e:
                print(f"[Worker-{worker_id}] ⚠️ 处理任务时发生错误: {e}")
                # 尝试关闭并重新打开标签页以恢复
                if worker_page:
                    try:
                        worker_page.close()
                    except Exception as close_e:
                        print(f"[Worker-{worker_id}] 尝试关闭页面时发生错误: {close_e}")
                    # 重新创建标签页
                    worker_page = browser_instance.new_tab()
                task_queue.task_done()  # 即使出错，也标记任务完成，避免队列阻塞

    except Exception as e:
        print(f"[Worker-{worker_id}] ⚠️ 工作线程初始化或主要循环中发生错误: {e}")
    finally:
        if worker_page:
            worker_page.close()  # 关闭工作线程使用的标签页
        print(f"[Worker-{worker_id}] 工作线程结束。")


# --- 监控线程函数 ---
def monitor_thread_func(browser_instance):
    """
    监控线程函数，负责定期刷新主页并识别新任务。
    使用同一个浏览器实例下的一个新标签页。
    """
    print("🚀 [Monitor] 启动监控线程...")
    monitor_page = None
    try:
        # 在同一个浏览器实例中创建新的标签页
        monitor_page = browser_instance.new_tab()
        print(f"[Monitor] 🔗 访问监控页面: {LOWENDTALK_MONITOR_URL}")
        monitor_page.get(LOWENDTALK_MONITOR_URL)

        # 显式等待帖子列表容器出现，增加鲁棒性
        post_items_container = monitor_page.ele('tag:ul@class=DataList Discussions', timeout=30)
        print("[Monitor] ✅ 监控页面加载完成，帖子列表容器已找到。")

        # --- 首次任务扫描与提交 ---
        # 从共享状态中加载最新的 processed_posts
        with shared_data_lock:
            crawl_state = load_state()
            processed_posts = crawl_state.get("processed_posts", {})

        if not post_items_container:  # 理论上上面 ele() 的 timeout 会捕获，但这里再加一层检查
            print("[Monitor] ❌ 首次加载时未找到包含帖子列表的 tag:ul@class=DataList Discussions 元素。")
            # 如果首次加载失败，则不设置 ready event，让工作线程超时退出
            time.sleep(30)  # 等待并重试
            return  # 首次失败则结束监控线程

        all_list_items = post_items_container.eles('xpath:.//li[contains(@class, "ItemDiscussion")]')
        non_announcement_posts = []
        for item in all_list_items:
            if not item.ele('.Tag-Announcement'):
                non_announcement_posts.append(item)

        posts_to_check = non_announcement_posts[:5]

        print(f"[Monitor] ✨ 首次扫描发现 {len(posts_to_check)} 个非公告帖子（限制为前 {len(posts_to_check)} 个）...")

        for item in posts_to_check:
            itemContent = item.ele('.ItemContent.Discussion')
            if not itemContent:
                continue
            postTitle_ele = itemContent.ele('.Title').ele("tag:a")
            post_title = postTitle_ele.text
            post_url = postTitle_ele.link
            if not post_url.startswith('http'):
                post_url = LOWENDTALK_BASE_URL.rstrip('/') + post_url

            current_comment_count = 0
            comment_count_span = itemContent.ele('.Meta.Meta-Discussion').s_ele('.MItem.MCount.CommentCount').ele(
                '.Number')
            if comment_count_span:
                count_text = comment_count_span.text.replace('K', '').replace('k', '')
                try:
                    if '.' in count_text:
                        current_comment_count = int(float(count_text) * 1000)
                    else:
                        current_comment_count = int(count_text)
                except ValueError:
                    pass

            last_comment_date_element = itemContent.ele('.Meta.Meta-Discussion').s_ele('.MItem.LastCommentDate').s_ele(
                'tag:time')
            datetime_attr = None
            current_last_comment_datetime_utc = None
            if last_comment_date_element:
                datetime_attr = last_comment_date_element.attr('datetime')
                if datetime_attr:
                    current_last_comment_datetime_utc = parse_time_string(datetime_attr)

            utc_minus_8_offset = timezone(timedelta(hours=-8))
            display_last_comment_datetime_utc8 = None
            if current_last_comment_datetime_utc:
                display_last_comment_datetime_utc8 = current_last_comment_datetime_utc.astimezone(utc_minus_8_offset)

            print(f"\n--- [Monitor] 首次检查帖子: {post_title} ---")
            print(f"[Monitor]    URL: {post_url}")
            print(
                f"[Monitor]    当前评论数: {current_comment_count}, 最新回复时间 (UTC-8): {display_last_comment_datetime_utc8}")

            should_add_to_queue = False
            prev_state = processed_posts.get(post_url, {})
            prev_comment_count = prev_state.get("last_comment_count", 0)
            prev_last_comment_datetime_str = prev_state.get("last_comment_datetime")
            prev_last_comment_datetime_utc = parse_time_string(prev_last_comment_datetime_str)

            if post_url not in processed_posts:
                print("[Monitor]    [NEW] 新发现的帖子，添加到任务队列。")
                should_add_to_queue = True
            else:
                if current_comment_count > prev_comment_count:
                    print(
                        f"[Monitor]    [UPDATE] 评论数量增加 ({prev_comment_count} -> {current_comment_count})，添加到任务队列。")
                    should_add_to_queue = True
                elif current_last_comment_datetime_utc and prev_last_comment_datetime_utc and \
                        current_last_comment_datetime_utc > prev_last_comment_datetime_utc:
                    print(
                        f"[Monitor]    [UPDATE] 最新回复时间更新 ({prev_last_comment_datetime_str} -> {datetime_attr})，添加到任务队列。")
                    should_add_to_queue = True
                else:
                    print("[Monitor]    [SKIPPED] 帖子没有更新，跳过。")
                    with shared_data_lock:
                        current_state = load_state()
                        current_state.get("processed_posts", {})[post_url] = {
                            "last_comment_count": current_comment_count,
                            "last_comment_datetime": datetime_attr,
                            "processed_comment_ids": prev_state.get("processed_comment_ids", [])
                        }
                        save_state(current_state)
                    continue

            if should_add_to_queue:
                task_queue.put({
                    "post_url": post_url,
                    "post_title": post_title,
                    "current_comment_count": current_comment_count,
                    "datetime_attr": datetime_attr
                })
                print(f"[Monitor]    帖子 '{post_title}' 已加入任务队列。")

        # 首次扫描完成后，设置事件，通知工作线程可以开始工作了
        monitor_ready_event.set()
        print("[Monitor] ✨ 监控线程已就绪，已发送就绪信号给工作线程。")

        # --- 定期刷新与检查 (循环部分) ---
        while True:
            print("\n--- [Monitor] 刷新监控页面并检查新任务 ---")
            monitor_page.refresh()

            # 再次获取元素，并设置超时，以防刷新后页面结构有短暂变化
            post_items_container = monitor_page.ele('tag:ul@class=DataList Discussions', timeout=30)
            time.sleep(3)

            with shared_data_lock:
                crawl_state = load_state()
                processed_posts = crawl_state.get("processed_posts", {})

            if not post_items_container:
                print("[Monitor] ❌ 刷新后未找到包含帖子列表的 tag:ul@class=DataList Discussions 元素。")
                time.sleep(30)
                continue

            all_list_items = post_items_container.eles('xpath:.//li[contains(@class, "ItemDiscussion")]')
            non_announcement_posts = []
            for item in all_list_items:
                if not item.ele('.Tag-Announcement'):
                    non_announcement_posts.append(item)

            posts_to_check = non_announcement_posts[:5]

            print(f"[Monitor] ✨ 发现 {len(posts_to_check)} 个非公告帖子（限制为前 {len(posts_to_check)} 个）...")

            for item in posts_to_check:
                itemContent = item.ele('.ItemContent.Discussion')
                if not itemContent:
                    continue
                postTitle_ele = itemContent.ele('.Title').ele("tag:a")
                post_title = postTitle_ele.text
                post_url = postTitle_ele.link
                if not post_url.startswith('http'):
                    post_url = LOWENDTALK_BASE_URL.rstrip('/') + post_url

                current_comment_count = 0
                comment_count_span = itemContent.ele('.Meta.Meta-Discussion').s_ele('.MItem.MCount.CommentCount').ele(
                    '.Number')
                if comment_count_span:
                    count_text = comment_count_span.text.replace('K', '').replace('k', '')
                    try:
                        if '.' in count_text:
                            current_comment_count = int(float(count_text) * 1000)
                        else:
                            current_comment_count = int(count_text)
                    except ValueError:
                        pass

                last_comment_date_element = itemContent.ele('.Meta.Meta-Discussion').s_ele(
                    '.MItem.LastCommentDate').s_ele('tag:time')
                datetime_attr = None
                current_last_comment_datetime_utc = None
                if last_comment_date_element:
                    datetime_attr = last_comment_date_element.attr('datetime')
                    if datetime_attr:
                        current_last_comment_datetime_utc = parse_time_string(datetime_attr)

                utc_minus_8_offset = timezone(timedelta(hours=-8))
                display_last_comment_datetime_utc8 = None
                if current_last_comment_datetime_utc:
                    display_last_comment_datetime_utc8 = current_last_comment_datetime_utc.astimezone(
                        utc_minus_8_offset)

                print(f"\n--- [Monitor] 检查帖子: {post_title} ---")
                print(f"[Monitor]    URL: {post_url}")
                print(
                    f"[Monitor]    当前评论数: {current_comment_count}, 最新回复时间 (UTC-8): {display_last_comment_datetime_utc8}")

                should_add_to_queue = False
                prev_state = processed_posts.get(post_url, {})
                prev_comment_count = prev_state.get("last_comment_count", 0)
                prev_last_comment_datetime_str = prev_state.get("last_comment_datetime")
                prev_last_comment_datetime_utc = parse_time_string(prev_last_comment_datetime_str)

                if post_url not in processed_posts:
                    print("[Monitor]    [NEW] 新发现的帖子，添加到任务队列。")
                    should_add_to_queue = True
                else:
                    if current_comment_count > prev_comment_count:
                        print(
                            f"[Monitor]    [UPDATE] 评论数量增加 ({prev_comment_count} -> {current_comment_count})，添加到任务队列。")
                        should_add_to_queue = True
                    elif current_last_comment_datetime_utc and prev_last_comment_datetime_utc and \
                            current_last_comment_datetime_utc > prev_last_comment_datetime_utc:
                        print(
                            f"[Monitor]    [UPDATE] 最新回复时间更新 ({prev_last_comment_datetime_str} -> {datetime_attr})，添加到任务队列。")
                        should_add_to_queue = True
                    else:
                        print("[Monitor]    [SKIPPED] 帖子没有更新，跳过。")
                        with shared_data_lock:
                            current_state = load_state()
                            current_state.get("processed_posts", {})[post_url] = {
                                "last_comment_count": current_comment_count,
                                "last_comment_datetime": datetime_attr,
                                "processed_comment_ids": prev_state.get("processed_comment_ids", [])
                            }
                            save_state(current_state)
                        continue

                if should_add_to_queue:
                    task_queue.put({
                        "post_url": post_url,
                        "post_title": post_title,
                        "current_comment_count": current_comment_count,
                        "datetime_attr": datetime_attr
                    })
                    print(f"[Monitor]    帖子 '{post_title}' 已加入任务队列。")

            print("\n--- [Monitor] 等待下一次刷新... ---")
            time.sleep(60)  # 每 60 秒刷新一次主页面

    except Exception as e:
        print(f"❌ [Monitor] 监控线程运行时发生错误: {e}")
    finally:
        if monitor_page:
            monitor_page.close()
        print("[Monitor] 监控线程结束。")


worker_threads = []
# --- 主程序逻辑 ---
def lowendtalk_spider_multi_threaded():
    print("🚀 启动 LowEndTalk 闪购爬虫 (多线程模式，单浏览器多标签页)...")

    # 关键设置：禁用单例标签页对象，这样每次 new_tab() 都会返回新的 Page 对象
    Settings.set_singleton_tab_obj(False)

    # 初始化一个 Chromium 浏览器实例
    browser = None
    try:
        browser = Chromium()
        # # 关闭默认打开的空白标签页，节省资源
        # if len(browser.tabs) > 0:
        #     browser.tabs[0].close()

        num_workers = 5  # 5 个工作线程

        # 启动监控线程
        # 将 browser 实例传递给线程函数
        monitor_thread = threading.Thread(target=monitor_thread_func, args=(browser,), daemon=True)
        monitor_thread.start()

        # 启动工作线程
        for i in range(num_workers):
            # 将 browser 实例传递给线程函数
            thread = threading.Thread(target=worker_thread_func, args=(i + 1, browser,), daemon=True)
            thread.start()
            worker_threads.append(thread)
        print(f"已启动 {num_workers} 个工作线程。")

        # 主线程可以做一些其他事情，或者简单地等待
        while True:
            time.sleep(1)  # 保持主线程活跃

    except KeyboardInterrupt:
        print("\n👋 收到停止信号 (Ctrl+C)，正在关闭爬虫...")
    except Exception as e:
        print(f"❌ 主程序运行时发生错误: {e}")
    finally:
        # 确保所有任务完成
        print("等待所有任务完成...")
        try:
            # 增加一个超时，防止无限等待
            task_queue.join(timeout=30)  # 等待任务队列清空，最多30秒
        except Exception as e:
            print(f"⚠️ 等待任务队列完成时发生错误: {e}")

        # 给工作线程一些时间来处理最后的任务和关闭标签页
        print("等待工作线程结束...")
        for thread in worker_threads:
            thread.join(timeout=15)  # 等待线程结束，设置超时
            if thread.is_alive():
                print(f"⚠️ 线程 {thread.name} 未能在超时时间内结束。")

        # 保存本次运行发现的所有闪购信息
        save_results(all_flash_sales_found_this_run)

        # 最终保存一次状态，确保任何未被工作线程保存的更新也写入文件
        final_state = load_state()
        save_state(final_state)

        if browser:
            print("正在关闭浏览器实例...")
            browser.quit()

        print(f"找到的闪购信息已保存到: {RESULTS_FILE}")
        print(f"爬取状态已保存到: {STATE_FILE}")
        print("爬虫已安全关闭。")


# --- 运行爬虫 ---
if __name__ == "__main__":
    lowendtalk_spider_multi_threaded()