import asyncio
import re
from curl_cffi import requests
from bs4 import BeautifulSoup
import chardet
from concurrent.futures import ThreadPoolExecutor
import re
import time
import asyncio
import httpx
from bs4 import BeautifulSoup
from urllib.parse import urljoin  # To join relative URLs to the base URL
from curl_cffi import requests

# 异步请求函数
async def request_fetch(session, url, params=None, cookies=None, headers=None,follow_redirects=None):
    try:
        # 发送GET请求，自动跟随重定向
        response = await session.get(url, params=params, cookies=cookies, headers=headers,follow_redirects=follow_redirects)
        # 检查是否发生了重定向
        if response.status_code == 302:
            # Get the full redirected URL
            redirected_url = urljoin('https://billing.spartanhost.net', response.headers.get('Location'))
            print(f"Redirected to {redirected_url}")
            # Return the new redirected response
            response = await session.get(redirected_url, cookies=cookies, headers=headers)
        return response
    except httpx.RequestError as e:
        print(f"Request failed: {e}")
        return None

async def post_request(session, url, params=None, data=None, cookies=None, headers=None):
    try:
        response = await session.post(url, params=params, data=data, cookies=cookies, headers=headers)
        # 检查是否发生了重定向
        if response.status_code == 302:
            # Get the full redirected URL
            redirected_url = urljoin('https://billing.spartanhost.net', response.headers.get('Location'))
            print(f"Redirected to {redirected_url}")
            # Return the new redirected response
            response = await session.get(redirected_url, cookies=cookies, headers=headers)
        return response
    except httpx.RequestError as e:
        print(f"Request failed: {e}")
        return None


async def buy(link):
    start_time = time.perf_counter()

    cookies = {
        'WHMCSYGwdvD9rORze': 'dnr8oor9r1a4lc2ggov1p6it4o'
    }

    headers = {
        'authority': 'billing.spartanhost.net',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        # 'cookie': 'WHMCSYGwdvD9rORze=9npir5trkvc5b5dklkn918k1a7; __stripe_mid=049af033-8138-4fc6-b45c-d130e3cb8e5aace305; __stripe_sid=28351adc-2687-4e47-8c28-97e472f309cc0e2039',
        'referer': 'https://billing.spartanhost.net/store/dallas-premium-vps',
        'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    data = {
        'ajax': '1',
        'a': 'confproduct',
        'configure': 'true',
        'i': '0',
        'billingcycle': 'monthly',
        'hostname': '37145.mowangk.org',
        'rootpw': 'laeEROgKpvVbekDYOM37',
        'ns1prefix': 'ns1',
        'ns2prefix': 'ns2'
    }

    async with httpx.AsyncClient() as session:
        print(link)
        link='https://billing.spartanhost.net/cart.php?a=add&pid=317&promocode=bf2024flash-2SFT1ZWRZR'
        # 第一步请求网页
        response = requests.get(
            link,
            cookies=cookies,
            headers=headers,
        )

        # response = await fetch(session, 'https://billing.spartanhost.net/cart.php?a=add&pid=412&promocode=bf2024flash-2SFT1ZWRZR', cookies=cookies,
        #                        headers=headers,follow_redirects=True)
        soup = BeautifulSoup(response.text, 'html.parser')

        # 正则表达式匹配 configoption[数字]
        pattern = re.compile(r'customfield\[\d+\]')
        input_elements = soup.find_all('input', {'name': pattern})

        for input_elem in input_elements:
            name = input_elem.get('name')
            data.update({name: 'on'})

        pattern1 = re.compile(r'configoption\[\d+\]')
        select_elements = soup.find_all('select', {'name': pattern1})

        for select in select_elements:
            name = select.get('name')
            default_value = select.find('option', {'selected': 'selected'})['value']
            data.update({name: default_value})

        print(data)
        print(f'第一步', response.status_code)

        # 第二步请求确认商品
        params = {
            'a': 'confproduct',
            'i': '0',
        }
        response = await request_fetch(session, 'https://billing.spartanhost.net/cart.php', params=params, cookies=cookies,headers=headers)
        print(f'第二步', response.status_code)
        # 第三步请求商品配置
        response = await post_request(session, 'https://billing.spartanhost.net/cart.php', cookies=cookies,headers=headers, data=data)
        print(f'第三步', response.status_code)
        print(response.text)

        # # 第四步确认域名
        # params = {'a': 'confdomains'}
        # response = await fetch(session, 'https://billing.spartanhost.net/cart.php', params=params, cookies=cookies,
        #                        headers=headers)
        # print(f'第四步',response.status_code)

        # 第五步使用优惠码
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://billing.spartanhost.net',
            'priority': 'u=0, i',
            'referer': 'https://billing.spartanhost.net/cart.php?a=view',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        params = {'a': 'view'}
        viewData = ({
            'token': 'd4e24bd6f433bbd794911db95fe27c364075a47d',  # Replace with actual token
            'promocode': 'kvm20',
            'validatepromo': 'Validate Code',
        })
        response = await post_request(session, 'https://billing.spartanhost.net/cart.php', cookies=cookies,headers=headers, data=viewData, params=params)
        print(f'第五步', response.status_code)

        # 第六步结算
        params = {'a': 'checkout'}
        checkoutData = ({
            'token': 'd4e24bd6f433bbd794911db95fe27c364075a47d',  # Replace with actual token
            'checkout': 'true',
            'custtype': 'existing',
            'account_id': '30727',
            'firstname': 'Liam',
            'lastname': 'Dodd',
            'email': '<EMAIL>',
            'country-calling-code-phonenumber': '1',
            'phonenumber': '************',
            'companyname': '',
            'address1': '4253 Ford Street',
            'address2': '',
            'city': 'Oakland',
            'state': 'California',
            'postcode': '94607',
            'country': 'US',
            'applycredit': '1',
            'paymentmethod': 'dplstripe_alipay',
            'ccinfo': 'new',
            'ccnumber': '',
            'ccexpirydate': '',
            'cccvv': '',
            'ccdescription': '',
            'nostore': ['1', '0'],
            'notes': '',
            'accepttos': 'on',
        })
        response = await post_request(session, 'https://billing.spartanhost.net/cart.php', cookies=cookies,headers=headers, data=checkoutData, params=params)
        print(f'第六步', response.status_code)

    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    print(f"程序运行时间: {elapsed_time} 秒")




# Asynchronous function to fetch page content using curl_cffi in a ThreadPoolExecutor
async def fetch(url, headers):
    def fetch_sync():
        response = requests.get(url, headers=headers, impersonate="chrome110")
        return response

    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as pool:
        response = await loop.run_in_executor(pool, fetch_sync)
    return response


# Get the last page number
async def get_last_page_links(url, headers):
    response = await fetch(url, headers)
    if response.status_code == 200:
        detected_encoding = chardet.detect(response.content)['encoding']
        content = response.content.decode(detected_encoding if detected_encoding else 'utf-8')
        soup = BeautifulSoup(content, 'html.parser')
        pager_before = soup.find(id='PagerBefore')
        if pager_before:
            last_page_link = pager_before.find('a', class_='LastPage')
            if last_page_link:
                print(int(last_page_link.getText()))
                return int(last_page_link.getText())
    return 0


# Construct page links dynamically based on last page number
async def construct_page_links(url, headers):
    page_max_num = await get_last_page_links(url, headers)
    if page_max_num == 0:
        return []
    base_url = url + f'/p{page_max_num}'
    return base_url


# Search for comments that match the given keywords
async def search_comments_for_keywords(page_link, keywords, headers):
    response = await fetch(page_link, headers)
    if response.status_code == 200:
        soup = BeautifulSoup(response.content, 'html.parser')
        comments = soup.find_all('div', class_='Message')

        for comment in comments:
            if all(keyword in comment.text for keyword in keywords):
                print("Found comment text:", comment.text)

                # 通过正则提取 Promo Code
                promo_code_match = re.search(r'(?:code|Code|Promo code):\s*([A-Za-z0-9-]+)', comment.text)
                promo_code = promo_code_match.group(1) if promo_code_match else None

                # 通过查找 <a> 标签来获取链接
                link = None
                # 查找所有 <a> 标签并打印其 href 属性
                a_tags = comment.find_all('a', href=True)
                if a_tags:
                    for a_tag in a_tags:
                        link = a_tag['href']
                        if 'spartanhost' in link:
                            print("Found spartanhost.net link:", link)
                            await buy(link)





# Main function to run the scraping
async def main():
    url = "https://lowendtalk.com/discussion/200000/2024-black-friday-cyber-monday-flash-sale-megathread/p199"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://lowendtalk.com/'
    }

    page_links = await construct_page_links(url, headers)
    if page_links:
        await search_comments_for_keywords(page_links, ['AlphaHost','Coupon code'],headers)



# Run the main function
if __name__ == '__main__':
    asyncio.run(main())
