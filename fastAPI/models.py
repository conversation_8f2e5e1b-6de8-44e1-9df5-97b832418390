from pydantic import BaseModel
from typing import List, Optional
from database import get_db

class User(BaseModel):
    id: Optional[int] = None
    username: str
    userid: str
    phone: str

def create_user(user: User):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("INSERT INTO users (userid, username ,phone) VALUES (?, ? ,?)", (user.userid,user.username, user.phone))
    conn.commit()
    conn.close()

def update_user(user_id: int, user: User):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("UPDATE users SET user_id = ?,username = ? , phone = ? WHERE user_id = ?", ( user_id,user.username,user.phone,user.userid))
    conn.commit()
    conn.close()

def delete_user(user_id: int):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("DELETE FROM users WHERE user_id = ?", (user_id,))
    conn.commit()
    conn.close()

def get_user_by_phone(phone: str) -> Optional[User]:
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE phone = ?", (phone,))
    row = cursor.fetchone()
    conn.close()
    return dict(row) if row else None

def get_user_by_username(username: str) -> Optional[User]:
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
    row = cursor.fetchone()
    conn.close()
    return dict(row) if row else None

def get_user_by_userid(user_id: str) -> Optional[User]:
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
    row = cursor.fetchone()
    conn.close()
    return dict(row) if row else None

def get_all_users() -> List[User]:
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users")
    rows = cursor.fetchall()
    conn.close()
    return [dict(row) for row in rows]
