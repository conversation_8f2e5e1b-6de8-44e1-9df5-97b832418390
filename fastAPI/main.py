import sqlite3

from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from database import get_db, init_db

app = FastAPI()

@app.on_event("startup")
def on_startup():
    init_db()

# --- Data Models ---
class UserBase(BaseModel):
    user_id: str
    username: str
    phone_number: str

class UserCreate(UserBase):
    pass

class UserUpdate(BaseModel):
    user_id: Optional[str] = None
    username: Optional[str] = None
    phone_number: Optional[str] = None

class UserInDB(UserBase):
    id: int

# --- API Endpoints ---

@app.get("/")
async def read_root():
    return {"message": "Welcome to the FastAPI User Management API!"}

@app.post("/users/", response_model=UserInDB, summary="新增用户")
async def create_user(user: UserCreate):
    conn = get_db()
    cursor = conn.cursor()
    try:
        cursor.execute("INSERT INTO users (user_id, username,phone_number) VALUES (?,?, ?)",
                       (user.user_id,user.username, user.phone_number))
        conn.commit()
        user_id = cursor.lastrowid
        return UserInDB(id=user_id, **user.dict())
    except sqlite3.IntegrityError as e:
        error_message = str(e)
        if "UNIQUE constraint failed: users.user_id" in error_message:
            raise HTTPException(status_code=400, detail="User ID already exists! | 当前userId已经存在！")
        elif "UNIQUE constraint failed: users.username" in error_message:
            raise HTTPException(status_code=400, detail="Username already exists! | 当前姓名已经存在！")
        elif "UNIQUE constraint failed: users.phone_number" in error_message:
            raise HTTPException(status_code=400, detail="Phone number already exists! | 当前手机号已经存在！")
        else:
            raise
    finally:
        conn.close()

@app.get("/users/{userId}", response_model=UserInDB, summary="获取指定user_id的用户")
async def read_user(userId: int):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE user_id = ?", (userId,))
    row = cursor.fetchone()
    conn.close()
    if row:
        return UserInDB(**dict(row))
    raise HTTPException(status_code=404, detail="User not found")

@app.get("/users/username/{username}", response_model=UserInDB, summary="通过姓名查询用户")
async def read_user_by_phone(username: str):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
    row = cursor.fetchone()
    conn.close()
    if row:
        return UserInDB(**dict(row))
    raise HTTPException(status_code=404, detail="User not found")

@app.get("/users/phone/{phone_number}", response_model=UserInDB, summary="通过手机号查询用户")
async def read_user_by_phone(phone_number: str):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE phone_number = ?", (phone_number,))
    row = cursor.fetchone()
    conn.close()
    if row:
        return UserInDB(**dict(row))
    raise HTTPException(status_code=404, detail="User not found")

@app.get("/users/", response_model=List[UserInDB], summary="获取所有用户")
async def read_users():
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users")
    rows = cursor.fetchall()
    conn.close()
    return [UserInDB(**dict(row)) for row in rows]

@app.put("/users/{id}", response_model=UserInDB, summary="修改用户")
async def update_user(id: int, user: UserUpdate):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE id = ?", (id,))
    row = cursor.fetchone()
    if not row:
        conn.close()
        raise HTTPException(status_code=404, detail="User not found")

    current_data = dict(row)
    new_data = {
        "user_id": user.user_id or current_data["user_id"],
        "phone_number": user.phone_number or current_data["phone_number"]
    }

    # 检查唯一性冲突
    cursor.execute("SELECT id FROM users WHERE user_id = ? AND id != ?", (new_data["user_id"], id))
    if cursor.fetchone():
        conn.close()
        raise HTTPException(status_code=400, detail="User with this user_id already exists")

    cursor.execute("SELECT id FROM users WHERE phone_number = ? AND id != ?", (new_data["phone_number"], id))
    if cursor.fetchone():
        conn.close()
        raise HTTPException(status_code=400, detail="User with this phone number already exists")

    cursor.execute("UPDATE users SET user_id = ?, phone_number = ? WHERE id = ?",
                   (new_data["user_id"], new_data["phone_number"], id))
    conn.commit()
    conn.close()
    return UserInDB(id=id, **new_data)

@app.delete("/users/{id}", summary="删除用户")
async def delete_user(id: int):
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute("DELETE FROM users WHERE id = ?", (id,))
    conn.commit()
    conn.close()
    return {"message": f"User with ID {id} deleted successfully"}