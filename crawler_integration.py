"""
Integration utilities for connecting the forum crawler with the purchase automation system
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
import re
from dataclasses import dataclass

from improved_forum_crawler import ForumCrawler, CrawlerConfig
from improved_purchase_automation import PurchaseAutomation, PurchaseConfig


@dataclass
class IntegrationConfig:
    """Configuration for crawler-purchase integration"""
    # Crawler settings
    crawler_config_path: str = "crawler_config.json"
    purchase_config_path: str = "config.json"
    
    # Integration settings
    auto_purchase_enabled: bool = False
    confidence_threshold: float = 0.7
    max_purchases_per_session: int = 3
    purchase_delay_seconds: int = 30
    
    # Filtering settings
    target_providers: List[str] = None
    min_price_usd: float = 0.0
    max_price_usd: float = 1000.0
    required_keywords: List[str] = None
    excluded_keywords: List[str] = None
    
    def __post_init__(self):
        if self.target_providers is None:
            self.target_providers = ["spartanhost", "alphahost", "hostworld"]
        if self.required_keywords is None:
            self.required_keywords = ["vps", "hosting"]
        if self.excluded_keywords is None:
            self.excluded_keywords = ["sold out", "expired", "ended"]


class FlashSaleAnalyzer:
    """Analyzes flash sale data for purchase decisions"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.logger = logging.getLogger("integration.analyzer")
        
        # Compile regex patterns for better performance
        self.price_pattern = re.compile(
            r'\$(\d+(?:\.\d{2})?)\s*(?:/(?:month|mo|year|yr))?',
            re.IGNORECASE
        )
        self.provider_patterns = {
            provider: re.compile(rf'\b{re.escape(provider)}\b', re.IGNORECASE)
            for provider in self.config.target_providers
        }
    
    def analyze_flash_sale(self, flash_sale_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a flash sale for purchase viability"""
        analysis = {
            "should_purchase": False,
            "confidence": flash_sale_data.get("confidence", 0.0),
            "reasons": [],
            "warnings": [],
            "extracted_data": {}
        }
        
        comment_text = flash_sale_data.get("comment_text", "").lower()
        post_title = flash_sale_data.get("post_title", "").lower()
        full_text = f"{post_title} {comment_text}"
        
        # Extract price information
        price_info = self._extract_price_info(full_text)
        analysis["extracted_data"]["price_info"] = price_info
        
        # Check provider match
        provider_match = self._check_provider_match(full_text)
        analysis["extracted_data"]["provider"] = provider_match
        
        # Check required keywords
        keyword_score = self._check_keywords(full_text)
        analysis["extracted_data"]["keyword_score"] = keyword_score
        
        # Check exclusion criteria
        exclusion_issues = self._check_exclusions(full_text)
        analysis["warnings"].extend(exclusion_issues)
        
        # Make purchase decision
        decision_score = self._calculate_decision_score(
            flash_sale_data.get("confidence", 0.0),
            price_info,
            provider_match,
            keyword_score,
            exclusion_issues
        )
        
        analysis["decision_score"] = decision_score
        analysis["should_purchase"] = (
            decision_score >= self.config.confidence_threshold and
            len(exclusion_issues) == 0 and
            provider_match is not None
        )
        
        if analysis["should_purchase"]:
            analysis["reasons"].append("High confidence flash sale detected")
            analysis["reasons"].append(f"Provider match: {provider_match}")
            analysis["reasons"].append(f"Decision score: {decision_score:.2f}")
        
        return analysis
    
    def _extract_price_info(self, text: str) -> Dict[str, Any]:
        """Extract price information from text"""
        price_info = {
            "prices_found": [],
            "min_price": None,
            "max_price": None,
            "currency": "USD"
        }
        
        matches = self.price_pattern.findall(text)
        if matches:
            prices = [float(match) for match in matches]
            price_info["prices_found"] = prices
            price_info["min_price"] = min(prices)
            price_info["max_price"] = max(prices)
        
        return price_info
    
    def _check_provider_match(self, text: str) -> Optional[str]:
        """Check if text mentions target providers"""
        for provider, pattern in self.provider_patterns.items():
            if pattern.search(text):
                return provider
        return None
    
    def _check_keywords(self, text: str) -> Dict[str, Any]:
        """Check for required keywords"""
        keyword_score = {
            "required_found": 0,
            "total_required": len(self.config.required_keywords),
            "found_keywords": []
        }
        
        for keyword in self.config.required_keywords:
            if keyword.lower() in text:
                keyword_score["required_found"] += 1
                keyword_score["found_keywords"].append(keyword)
        
        keyword_score["score"] = (
            keyword_score["required_found"] / keyword_score["total_required"]
            if keyword_score["total_required"] > 0 else 1.0
        )
        
        return keyword_score
    
    def _check_exclusions(self, text: str) -> List[str]:
        """Check for exclusion criteria"""
        issues = []
        
        for excluded in self.config.excluded_keywords:
            if excluded.lower() in text:
                issues.append(f"Contains excluded keyword: {excluded}")
        
        return issues
    
    def _calculate_decision_score(
        self,
        base_confidence: float,
        price_info: Dict[str, Any],
        provider_match: Optional[str],
        keyword_score: Dict[str, Any],
        exclusion_issues: List[str]
    ) -> float:
        """Calculate overall decision score"""
        score = base_confidence
        
        # Boost for provider match
        if provider_match:
            score += 0.2
        
        # Boost for keyword match
        score += keyword_score["score"] * 0.1
        
        # Price range check
        if price_info["min_price"] is not None:
            if (self.config.min_price_usd <= price_info["min_price"] <= self.config.max_price_usd):
                score += 0.1
            else:
                score -= 0.2
        
        # Penalty for exclusions
        score -= len(exclusion_issues) * 0.3
        
        return max(0.0, min(1.0, score))


class PurchaseQueue:
    """Manages queued purchases with rate limiting"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.logger = logging.getLogger("integration.queue")
        self.purchase_queue = asyncio.Queue()
        self.purchases_made = 0
        self.last_purchase_time = 0
    
    async def add_purchase_candidate(self, flash_sale_data: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        """Add a purchase candidate to the queue"""
        if self.purchases_made >= self.config.max_purchases_per_session:
            self.logger.warning("Maximum purchases per session reached, skipping")
            return
        
        purchase_item = {
            "flash_sale_data": flash_sale_data,
            "analysis": analysis,
            "queued_at": datetime.now(timezone.utc).isoformat()
        }
        
        await self.purchase_queue.put(purchase_item)
        self.logger.info(f"Added purchase candidate to queue: {flash_sale_data.get('post_title', 'Unknown')[:50]}...")
    
    async def process_purchase_queue(self, purchase_automation: PurchaseAutomation) -> None:
        """Process items in the purchase queue"""
        while True:
            try:
                # Wait for items in queue
                purchase_item = await asyncio.wait_for(
                    self.purchase_queue.get(),
                    timeout=60.0
                )
                
                # Check rate limiting
                current_time = asyncio.get_event_loop().time()
                time_since_last = current_time - self.last_purchase_time
                
                if time_since_last < self.config.purchase_delay_seconds:
                    wait_time = self.config.purchase_delay_seconds - time_since_last
                    self.logger.info(f"Rate limiting: waiting {wait_time:.1f} seconds")
                    await asyncio.sleep(wait_time)
                
                # Attempt purchase
                success = await self._attempt_purchase(purchase_item, purchase_automation)
                
                if success:
                    self.purchases_made += 1
                    self.last_purchase_time = asyncio.get_event_loop().time()
                    
                    if self.purchases_made >= self.config.max_purchases_per_session:
                        self.logger.info("Maximum purchases reached, stopping purchase processing")
                        break
                
                self.purchase_queue.task_done()
                
            except asyncio.TimeoutError:
                # No items in queue, continue waiting
                continue
            except Exception as e:
                self.logger.error(f"Error processing purchase queue: {e}")
                await asyncio.sleep(5)
    
    async def _attempt_purchase(
        self,
        purchase_item: Dict[str, Any],
        purchase_automation: PurchaseAutomation
    ) -> bool:
        """Attempt to make a purchase"""
        flash_sale_data = purchase_item["flash_sale_data"]
        analysis = purchase_item["analysis"]
        
        self.logger.info(f"Attempting purchase: {flash_sale_data.get('post_title', 'Unknown')}")
        
        try:
            # Extract purchase URL from comment text
            comment_text = flash_sale_data.get("comment_text", "")
            purchase_urls = self._extract_purchase_urls(comment_text)
            
            if not purchase_urls:
                self.logger.warning("No purchase URLs found in comment")
                return False
            
            # Try each URL until one succeeds
            for url in purchase_urls:
                try:
                    self.logger.info(f"Attempting purchase from URL: {url}")
                    success = await purchase_automation.purchase_product(url)
                    
                    if success:
                        self.logger.info(f"Purchase successful from URL: {url}")
                        return True
                    else:
                        self.logger.warning(f"Purchase failed from URL: {url}")
                        
                except Exception as e:
                    self.logger.error(f"Error attempting purchase from {url}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Critical error in purchase attempt: {e}")
            return False
    
    def _extract_purchase_urls(self, text: str) -> List[str]:
        """Extract potential purchase URLs from text"""
        # Look for URLs that might be purchase links
        url_pattern = re.compile(
            r'https?://[^\s<>"\']+(?:cart|order|buy|purchase|billing)[^\s<>"\']*',
            re.IGNORECASE
        )
        
        urls = url_pattern.findall(text)
        
        # Filter for known hosting providers
        filtered_urls = []
        for url in urls:
            for provider in self.config.target_providers:
                if provider.lower() in url.lower():
                    filtered_urls.append(url)
                    break
        
        return filtered_urls


class CrawlerPurchaseIntegration:
    """Main integration class connecting crawler and purchase automation"""
    
    def __init__(self, integration_config: IntegrationConfig):
        self.config = integration_config
        self.logger = logging.getLogger("integration.main")
        self.analyzer = FlashSaleAnalyzer(integration_config)
        self.purchase_queue = PurchaseQueue(integration_config)
        
        # Load configurations
        self.crawler_config = CrawlerConfig.from_file(self.config.crawler_config_path)
        self.purchase_config = PurchaseConfig.from_file(self.config.purchase_config_path)
    
    async def run_integrated_system(self) -> None:
        """Run the integrated crawler and purchase system"""
        self.logger.info("Starting integrated crawler-purchase system")
        
        try:
            # Start purchase automation if enabled
            purchase_automation = None
            if self.config.auto_purchase_enabled:
                purchase_automation = PurchaseAutomation(self.purchase_config, self.logger)
                await purchase_automation.__aenter__()
                
                # Start purchase queue processor
                asyncio.create_task(self.purchase_queue.process_purchase_queue(purchase_automation))
            
            # Start crawler with callback for flash sales
            await self._run_crawler_with_callback()
            
        except Exception as e:
            self.logger.error(f"Error in integrated system: {e}")
        finally:
            if purchase_automation:
                await purchase_automation.__aexit__(None, None, None)
    
    async def _run_crawler_with_callback(self) -> None:
        """Run crawler with flash sale callback"""
        # This would need to be integrated with the actual crawler
        # For now, this is a placeholder showing the integration pattern
        
        def flash_sale_callback(flash_sale_data: Dict[str, Any]) -> None:
            """Callback function for when flash sales are detected"""
            asyncio.create_task(self._handle_flash_sale_detection(flash_sale_data))
        
        # The actual crawler would need to be modified to accept this callback
        # crawler = ForumCrawler(self.crawler_config)
        # crawler.set_flash_sale_callback(flash_sale_callback)
        # crawler.start()
        
        self.logger.info("Crawler integration placeholder - implement actual callback mechanism")
    
    async def _handle_flash_sale_detection(self, flash_sale_data: Dict[str, Any]) -> None:
        """Handle detected flash sale"""
        self.logger.info(f"Flash sale detected: {flash_sale_data.get('post_title', 'Unknown')[:50]}...")
        
        # Analyze the flash sale
        analysis = self.analyzer.analyze_flash_sale(flash_sale_data)
        
        self.logger.info(f"Analysis result - Should purchase: {analysis['should_purchase']}, "
                        f"Score: {analysis.get('decision_score', 0):.2f}")
        
        # If auto-purchase is enabled and analysis is positive, queue for purchase
        if self.config.auto_purchase_enabled and analysis["should_purchase"]:
            await self.purchase_queue.add_purchase_candidate(flash_sale_data, analysis)
        
        # Log the analysis for manual review
        self._log_analysis_result(flash_sale_data, analysis)
    
    def _log_analysis_result(self, flash_sale_data: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        """Log analysis result for review"""
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "post_title": flash_sale_data.get("post_title"),
            "post_url": flash_sale_data.get("post_url"),
            "comment_id": flash_sale_data.get("comment_id"),
            "analysis": analysis,
            "auto_purchase_enabled": self.config.auto_purchase_enabled
        }
        
        # Save to analysis log file
        try:
            with open("flash_sale_analysis.jsonl", "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        except Exception as e:
            self.logger.error(f"Failed to write analysis log: {e}")


async def main():
    """Example usage of the integration system"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create integration configuration
    integration_config = IntegrationConfig(
        auto_purchase_enabled=False,  # Set to True to enable auto-purchasing
        confidence_threshold=0.7,
        max_purchases_per_session=2,
        target_providers=["spartanhost", "alphahost"],
        required_keywords=["vps", "hosting", "server"]
    )
    
    # Run integrated system
    integration = CrawlerPurchaseIntegration(integration_config)
    await integration.run_integrated_system()


if __name__ == "__main__":
    asyncio.run(main())
