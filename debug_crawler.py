"""
Debug version of the forum crawler to identify the issue
"""

import time
import logging
from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_forum_access():
    """Debug forum access to identify the issue"""
    logger.info("Starting debug forum access...")
    
    try:
        # Configure DrissionPage
        Settings.set_singleton_tab_obj(False)
        
        # Create browser
        browser = Chromium()
        logger.info("<PERSON>rowser created successfully")
        
        # Create tab
        page = browser.new_tab()
        logger.info("Tab created successfully")
        
        # Navigate to forum
        url = "https://lowendtalk.com/categories/offers"
        logger.info(f"Navigating to: {url}")
        
        page.get(url)
        logger.info("Page loaded, waiting...")
        
        # Wait for page to load
        time.sleep(5)
        
        # Check page title
        try:
            title = page.title
            logger.info(f"Page title: {title}")
        except Exception as e:
            logger.error(f"Could not get page title: {e}")
        
        # Try to find elements step by step
        logger.info("Looking for page elements...")
        
        # Check if page loaded properly
        try:
            body = page.ele('body')
            if body:
                logger.info("Found body element")
            else:
                logger.error("No body element found")
        except Exception as e:
            logger.error(f"Error finding body: {e}")
        
        # Try different selectors for posts
        selectors_to_test = [
            'tag:ul@class=DataList Discussions',
            '.DataList.Discussions',
            'ul.DataList.Discussions',
            '.DataList',
            '.ItemDiscussion',
            'li.ItemDiscussion',
            '.Discussion'
        ]
        
        for selector in selectors_to_test:
            try:
                logger.info(f"Testing selector: {selector}")
                elements = page.eles(selector, timeout=5)
                if elements:
                    logger.info(f"✅ Found {len(elements)} elements with selector: {selector}")
                    
                    # If we found discussion items, try to get some basic info
                    if 'ItemDiscussion' in selector or 'Discussion' in selector:
                        for i, elem in enumerate(elements[:3]):  # Check first 3
                            try:
                                # Try to find title
                                title_elem = elem.ele('.Title')
                                if title_elem:
                                    title_text = title_elem.text
                                    logger.info(f"  Post {i+1} title: {title_text[:50]}...")
                            except Exception as e:
                                logger.warning(f"  Could not get title for post {i+1}: {e}")
                else:
                    logger.warning(f"❌ No elements found with selector: {selector}")
            except Exception as e:
                logger.error(f"❌ Error with selector {selector}: {e}")
        
        # Check if we can find any links or content
        try:
            links = page.eles('a')
            logger.info(f"Found {len(links)} links on page")
        except Exception as e:
            logger.error(f"Error finding links: {e}")
        
        # Get page source length to check if content loaded
        try:
            page_source = page.html
            logger.info(f"Page source length: {len(page_source)} characters")
            
            # Check for common forum elements
            if 'lowendtalk' in page_source.lower():
                logger.info("✅ Page contains 'lowendtalk' - likely correct site")
            else:
                logger.warning("❌ Page does not contain 'lowendtalk' - may be wrong page")
                
            if 'discussion' in page_source.lower():
                logger.info("✅ Page contains 'discussion' - likely forum content")
            else:
                logger.warning("❌ Page does not contain 'discussion' - may not be forum")
                
        except Exception as e:
            logger.error(f"Error getting page source: {e}")
        
        logger.info("Debug completed. Check logs above for issues.")
        
    except Exception as e:
        logger.error(f"Critical error in debug: {e}")
    finally:
        try:
            if 'browser' in locals():
                browser.quit()
                logger.info("Browser closed")
        except Exception as e:
            logger.error(f"Error closing browser: {e}")

if __name__ == "__main__":
    debug_forum_access()
