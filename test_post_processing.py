"""
Test script to debug post processing issues
"""

import time
import logging
from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_post_processing():
    """Test post processing to find the bottleneck"""
    logger.info("Starting post processing test...")
    
    try:
        # Configure DrissionPage
        Settings.set_singleton_tab_obj(False)
        
        # Create browser
        browser = Chromium()
        page = browser.new_tab()
        
        # Navigate to forum
        url = "https://lowendtalk.com/categories/offers"
        logger.info(f"Navigating to: {url}")
        page.get(url)
        time.sleep(5)
        
        # Find the main container
        container = page.ele('tag:ul@class=DataList Discussions', timeout=10)
        if not container:
            logger.error("Could not find container")
            return
        
        # Get posts
        posts = container.eles('xpath:./li[contains(@class, "Item")]')
        logger.info(f"Found {len(posts)} posts")
        
        # Filter announcements
        non_announcement_posts = []
        for i, post in enumerate(posts):
            try:
                logger.info(f"Checking post {i+1} for announcements...")
                announcement = post.ele('.Tag-Announcement')
                if not announcement:
                    non_announcement_posts.append(post)
                    logger.info(f"  Post {i+1}: Not an announcement")
                else:
                    logger.info(f"  Post {i+1}: Is an announcement, skipping")
            except Exception as e:
                logger.error(f"  Error checking post {i+1}: {e}")
                # Include it anyway if we can't check
                non_announcement_posts.append(post)
        
        logger.info(f"After filtering: {len(non_announcement_posts)} non-announcement posts")
        
        # Test extracting data from first few posts
        posts_to_test = non_announcement_posts[:3]  # Test first 3
        
        for i, post in enumerate(posts_to_test):
            logger.info(f"\n--- Testing post {i+1} data extraction ---")
            try:
                # Extract basic info
                item_content = post.ele('.ItemContent.Discussion')
                if not item_content:
                    logger.warning(f"Post {i+1}: No ItemContent found")
                    continue
                
                # Extract title
                title_element = item_content.ele('.Title').ele("tag:a")
                if title_element:
                    title = title_element.text.strip()
                    url = title_element.link
                    logger.info(f"Post {i+1} title: {title[:50]}...")
                    logger.info(f"Post {i+1} URL: {url}")
                else:
                    logger.warning(f"Post {i+1}: No title found")
                    continue
                
                # Extract comment count
                comment_count = 0
                comment_element = item_content.ele('.Meta.Meta-Discussion').s_ele('.MItem.MCount.CommentCount').ele('.Number')
                if comment_element:
                    count_text = comment_element.text
                    logger.info(f"Post {i+1} comment count text: '{count_text}'")
                    try:
                        if 'k' in count_text.lower():
                            count_text = count_text.replace('K', '').replace('k', '')
                            if '.' in count_text:
                                comment_count = int(float(count_text) * 1000)
                            else:
                                comment_count = int(count_text) * 1000
                        else:
                            comment_count = int(count_text)
                        logger.info(f"Post {i+1} comment count: {comment_count}")
                    except ValueError as e:
                        logger.warning(f"Post {i+1}: Could not parse comment count '{count_text}': {e}")
                
                # Extract date
                date_element = item_content.ele('.Meta.Meta-Discussion').s_ele('.MItem.LastCommentDate').s_ele('tag:time')
                if date_element:
                    datetime_attr = date_element.attr('datetime')
                    logger.info(f"Post {i+1} datetime: {datetime_attr}")
                else:
                    logger.warning(f"Post {i+1}: No datetime found")
                
                logger.info(f"Post {i+1}: Data extraction successful")
                
            except Exception as e:
                logger.error(f"Post {i+1}: Error in data extraction: {e}")
                continue
        
        logger.info("Post processing test completed successfully")
        
    except Exception as e:
        logger.error(f"Critical error in test: {e}")
    finally:
        try:
            if 'browser' in locals():
                browser.quit()
        except Exception as e:
            logger.error(f"Error closing browser: {e}")

if __name__ == "__main__":
    test_post_processing()
