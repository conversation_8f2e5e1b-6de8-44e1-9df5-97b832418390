import pandas as pd
from collections import defaultdict

# 数据
data = [
    ['SkyMatrix', None, None, '意大利', None, '商品有缺陷或无法使用', None, None, 8.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '网站描述不准确', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '缺少配件或损坏', None, None, 2.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '错发商品', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '派送失败', None, None, 13.0, None, None, None, None],
    [None, None, None, None, None, '商品和运输箱均损坏', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '没有给出理由', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, '希腊', None, '商品有缺陷或无法使用', None, None, 12.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, '西班牙', None, '不再需要', None, None, 6.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '商品有缺陷或无法使用', None, None, 18.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '没有给出理由', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '商品有缺陷或无法使用', None, None, 1.0, None, None, None, None],
    [None, None, None, '瑞典', None, '错误购买', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '派送失败', None, None, 11.0, None, None, None, None],
    [None, None, None, None, None, '商品有缺陷或无法使用', None, None, 13.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '网站描述不准确', None, None, 7.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '没有给出理由', None, None, 3.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, '葡萄牙', None, '未批准购买', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, '罗马尼亚', None, '商品损坏，但运输箱完好', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '派送失败', None, None, 8.0, None, None, None, None],
    [None, None, None, None, None, '没有给出理由', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '商品送达太晚', None, None, 1.0, None, None, None, None],
    [None, None, None, '荷兰', None, None, None, None, None, None, None, None, None, None, None, '错发商品', None, None, 1.0, None, None, None, None],
    [None, None, None, None, None, '错发商品', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '商品有缺陷或无法使用', None, None, 9.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '派送失败', None, None, 6.0, None, None, None, None],
    [None, None, None, '法国', None, '找到了更优惠的价格', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '不再需要', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '派送失败', None, None, 9.0, None, None, None, None],
    [None, None, None, None, None, '商品有缺陷或无法使用', None, None, 11.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '网站描述不准确', None, None, 4.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '错发商品', None, None, 1.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, '没有给出理由', None, None, 6.0, None, None, None, None, None, None, None, None, None, None, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '商品送达太晚', None, None, 1.0, None, None, None, None],
    [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, '还没有收到包裹', None, None, 1.0, None, None, None, None]
]

# 创建DataFrame，并给列命名，方便后续操作
df = pd.DataFrame(data, columns=[
    '店铺', 'col_1', 'col_2', '国家', 'col_4', '退货退款原因', 'col_6', 'col_7', '退货退款数量',
    'col_9', 'col_10', 'col_11', 'col_12', 'col_13', 'col_14', '仅退款原因', 'col_16', 'col_17', '仅退款数量',
    'col_19', 'col_20', 'col_21', 'col_22'
])

# 填充国家列（向下填充）
df['国家'] = df['国家'].fillna(method='ffill')

# 初始化统计字典
stats = defaultdict(lambda: {'仅退款': 0, '部分退款': 0, '退货退款': 0})

# 遍历DataFrame进行统计
for index, row in df.iterrows():
    country = row['国家']

    # 统计退货退款
    if pd.notna(row['退货退款原因']) and pd.notna(row['退货退款数量']):
        stats[country]['退货退款'] += int(row['退货退款数量'])

    # 统计仅退款
    if pd.notna(row['仅退款原因']) and pd.notna(row['仅退款数量']):
        stats[country]['仅退款'] += int(row['仅退款数量'])

    # 题目数据中没有明确的“部分退款”列，根据您提供的图片，“部分退款”总是0。
    # 如果实际数据中存在，需要根据数据结构调整此处逻辑。
    # stats[country]['部分退款'] += ...

# 构建最终的表格数据
table_data = []
# 用于记录店铺名称是否已添加，因为SkyMatrix只在第一行出现
shop_added = False

for country, counts in stats.items():
    row_data = {
        '店铺': 'SkyMatrix' if not shop_added else '', # 只有第一行显示店铺名称
        '国家': country,
        '实际销量': '', # 留空
        '仅退款': counts['仅退款'],
        '部分退款': counts['部分退款'], # 根据原始图片，这里是0
        '退货退款': counts['退货退款'],
        '总销量': counts['仅退款'] + counts['部分退款'] + counts['退货退款'],
        '退款率': '' # 留空
    }
    table_data.append(row_data)
    shop_added = True # 标记店铺名称已添加

# 创建最终的DataFrame用于展示
final_df = pd.DataFrame(table_data)

# 打印表格（可选，用于控制台预览）
print("最终结果表格：")
print(final_df.to_string(index=False))

# 将结果写入Excel文件
output_excel_file = 'refund_summary.xlsx'
final_df.to_excel(output_excel_file, index=False, engine='openpyxl')

print(f"\n统计结果已成功写入到 '{output_excel_file}'")