import json
import re
import time
from DrissionPage import ChromiumPage
from datetime import datetime, timedelta, timezone

# --- 配置参数 ---
LOWENDTALK_BASE_URL = "https://lowendtalk.com/"
# LOWENDTALK_OFFERS_URL = "https://lowendtalk.com/categories/offers"  # 建议从 'Offers' 版块开始，闪购信息更集中
LOWENDTALK_OFFERS_URL = "https://lowendtalk.com"
STATE_FILE = "lowendtalk_crawl_state.json"
RESULTS_FILE = "lowendtalk_flash_sales.json"

# 闪购识别关键词 (可以根据实际情况添加更多)
FLASH_SALE_KEYWORDS = [
    "offer", "sale", "discount", "promo", "limited", "flash sale",
    "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
    "$/month", "$/yr", "$/year", "$/mo"
]


# --- 辅助函数 ---

def load_state():
    """加载之前保存的爬取状态"""
    try:
        with open(STATE_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {"processed_posts": {}}


def save_state(state):
    """保存当前爬取状态"""
    with open(STATE_FILE, 'w', encoding='utf-8') as f:
        json.dump(state, f, ensure_ascii=False, indent=4)


def load_results():
    """加载之前发现的闪购信息"""
    try:
        with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []


def save_results(results):
    """保存发现的闪购信息 (增量追加并去重)"""
    existing_results = load_results()

    # 使用一个集合来存储现有结果的哈希值，便于快速去重
    existing_hashes = {hash(json.dumps(item, sort_keys=True)) for item in existing_results}

    new_items_added = 0
    for new_item in results:
        item_hash = hash(json.dumps(new_item, sort_keys=True))
        if item_hash not in existing_hashes:
            existing_results.append(new_item)
            existing_hashes.add(item_hash)
            new_items_added += 1

    if new_items_added > 0:
        with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(existing_results, f, ensure_ascii=False, indent=4)
        print(f"✅ 已保存 {new_items_added} 条新的闪购信息到 {RESULTS_FILE}")
    else:
        print("ℹ️ 未发现新的闪购信息需要保存。")


def is_flash_sale(text):
    """简单判断文本是否包含闪购关键词"""
    text_lower = text.lower()
    for keyword in FLASH_SALE_KEYWORDS:
        if keyword in text_lower:
            return True
    return False


def parse_time_string(time_str):
    """解析时间字符串为 datetime 对象，处理 '2:30AM' 这种不带日期的格式"""
    try:
        # 尝试解析完整日期时间
        return datetime.fromisoformat(time_str.replace('Z', '+00:00'))  # 处理可能存在的Z
    except ValueError:
        try:
            # 如果只包含时间，则补充今天的日期（假设是列表页的当前日期）
            # 注意：这里需要更严谨地从页面获取当前日期
            today = datetime.now().strftime("%Y-%m-%d")
            return datetime.strptime(f"{today} {time_str}", "%Y-%m-%d %I:%M%p")
        except ValueError:
            # 如果是 'July 13' 这种不带年份的，需要补充年份
            # 这是一个简化的处理，实际需要更精确的年份判断（例如，与当前年份对比）
            current_year = datetime.now().year
            try:
                # 尝试解析 'Month Day, Year Time'
                return datetime.strptime(f"{time_str} {current_year}", "%B %d %Y %I:%M%p")
            except ValueError:
                # 尝试解析 'Month Day' 格式
                try:
                    return datetime.strptime(f"{time_str}, {current_year}", "%B %d, %Y")
                except ValueError:
                    return None  # 无法解析的日期


# --- 主爬虫逻辑 ---

def lowendtalk_spider():
    print("🚀 启动 LowEndTalk 闪购爬虫...")

    page = ChromiumPage()
    crawl_state = load_state()
    # processed_posts 存储每个帖子的最新评论数和最新评论时间戳
    # {post_url: {"last_comment_count": N, "last_comment_datetime": "YYYY-MM-DDTHH:MM:SS+00:00"}}
    processed_posts = crawl_state.get("processed_posts", {})
    all_flash_sales_found_this_run = []  # 本次运行发现的闪购信息

    try:
        print(f"🔗 访问帖子列表页: {LOWENDTALK_OFFERS_URL}")
        page.get(LOWENDTALK_OFFERS_URL)
        # page.wait.load_start()
        # LowEndTalk 帖子列表通常是完整的，不需要滚动加载更多
        page.ele('tag:ul@class=DataList Discussions', timeout=30)
        print("[Monitor] ✅ 监控页面加载完成，帖子列表容器已找到。")

        post_items = page.eles(
            'xpath://li[contains(@class, "ItemDiscussion") and not(.//span[contains(@class, "Tag-Announcement")])]')[:5]
        print(f"✨ 发现 {len(post_items)} 个非公告帖子（限制为前 5 个）...")

        for item in post_items:

            # 提取帖子链接和标题
            itemContent  = item.ele('.ItemContent Discussion')
            if not itemContent :
                continue
            postTitle_ele=itemContent.ele('.Title').ele("tag:a")
            post_title = postTitle_ele.text
            post_url = postTitle_ele.link
            print(post_title)
            print(post_url)

            if not post_url.startswith('http'):
                post_url = LOWENDTALK_BASE_URL.rstrip('/') + post_url

            # 提取评论总数
            itemMeta  = item.ele('.:Meta Meta-Discussion')
            comment_count_element = itemMeta.s_ele('.:MItem DiscussionAuthor').s_ele('tag=a')
            author = comment_count_element.text
            print(author)

            current_comment_count = 0
            current_comment_count=itemMeta.s_ele('.:MItem MCount CommentCount').ele('.:Number').text
            print(current_comment_count)
            if comment_count_element:
                count_text = comment_count_element.text.replace('K', '000').replace('k', '000')  # '3.9K' -> '3900'
                try:
                    current_comment_count = int(float(count_text) * 1000) if '.' in count_text else int(count_text)
                except ValueError:
                    pass  # 无法解析时默认为0

            # 提取最后回复时间
            if current_comment_count!=0:
               last_comment_date_element = item.ele('.MItem LastCommentDate').s_ele('tag:time')
               current_last_comment_datetime = None
               datetime_attr = None
               if last_comment_date_element:
                   datetime_attr = last_comment_date_element.attr('datetime')
                   if datetime_attr:
                       current_last_comment_datetime = parse_time_string(datetime_attr)
               if current_last_comment_datetime:
                   utc_minus_8_offset = timezone(timedelta(hours=+8))
                   if current_last_comment_datetime.tzinfo is None:
                       current_last_comment_datetime = current_last_comment_datetime.replace(tzinfo=timezone.utc)
                   current_last_comment_datetime = current_last_comment_datetime.astimezone(utc_minus_8_offset)
            else:
                current_comment_count=None

            print(f"\n--- 处理帖子: {post_title} ---")
            print(f"   URL: {post_url}")
            print(f"   当前评论数: {current_comment_count}, 最新回复时间: {current_last_comment_datetime}")

            # --- 增量判断 ---
            should_crawl_details = False
            if post_url not in processed_posts:
                print("   [NEW] 新发现的帖子，需要抓取详情。")
                should_crawl_details = True
            else:
                # 比较评论总数或最新回复时间
                prev_state = processed_posts[post_url]
                prev_comment_count = prev_state.get("last_comment_count", 0)
                prev_last_comment_datetime_str = prev_state.get("last_comment_datetime")
                prev_last_comment_datetime = parse_time_string(prev_last_comment_datetime_str)

                if current_comment_count > prev_comment_count:
                    print(f"   [UPDATE] 评论数量增加 ({prev_comment_count} -> {current_comment_count})，需要抓取详情。")
                    should_crawl_details = True
                elif current_last_comment_datetime and prev_last_comment_datetime and \
                        current_last_comment_datetime > prev_last_comment_datetime:
                    print(
                        f"   [UPDATE] 最新回复时间更新 ({prev_last_comment_datetime_str} -> {datetime_attr})，需要抓取详情。")
                    should_crawl_details = True
                else:
                    print("   [SKIPPED] 帖子没有更新，跳过。")
                    processed_posts[post_url] = {  # 更新状态
                        "last_comment_count": current_comment_count,
                        "last_comment_datetime": datetime_attr
                    }
                    continue  # 跳过当前帖子，不进入详情页

            if should_crawl_details:
                # --- 进入帖子详情页抓取评论 ---
                post_detail_page = ChromiumPage()
                try:
                    print(f"   进入帖子详情页: {post_url}")
                    post_detail_page.get(post_url)
                    post_detail_page.wait.load_start()

                    # 滚动到页面底部以加载所有评论 (LowEndTalk 评论通常是动态加载的)
                    # 持续滚动直到不再出现新的评论或达到最大尝试次数
                    last_height = post_detail_page.scroll.to_bottom()
                    while True:
                        time.sleep(2)  # 等待加载
                        new_height = post_detail_page.scroll.to_bottom()
                        if new_height == last_height:
                            break  # 页面不再滚动，认为已加载所有评论
                        last_height = new_height

                    # 获取所有评论元素
                    comments = post_detail_page.eles('.Comment')
                    print(f"   成功抓取到 {len(comments)} 条评论。")

                    # 记录当前帖子已处理的评论ID，用于本次运行内的去重
                    current_post_processed_comment_ids = set()

                    for comment_element in comments:
                        comment_id_str = comment_element.attr('id')  # 获取评论的ID，例如 "Comment_12345"
                        if not comment_id_str:
                            continue  # 跳过没有ID的评论

                        # 检查此评论是否已在之前的运行中处理过
                        if comment_id_str in processed_posts.get(post_url, {}).get("processed_comment_ids", []):
                            continue  # 如果已处理，跳过

                        # 检查此评论是否在本次运行中已经处理过（防止重复处理，如果页面结构有重复元素）
                        if comment_id_str in current_post_processed_comment_ids:
                            continue

                        comment_text_element = comment_element.ele('.Message')
                        if not comment_text_element:
                            continue

                        comment_text = comment_text_element.text.strip()

                        # 检查是否包含闪购信息
                        if is_flash_sale(comment_text):
                            flash_sale_info = {
                                "post_title": post_title,
                                "post_url": post_url,
                                "comment_id": comment_id_str,
                                "comment_text": comment_text,
                                "crawled_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                            }
                            all_flash_sales_found_this_run.append(flash_sale_info)
                            print(f"      🎉 发现闪购信息！评论ID: {comment_id_str}")
                            print(f"         内容: {comment_text[:100]}...")  # 打印前100字符

                        # 将当前评论标记为已处理
                        current_post_processed_comment_ids.add(comment_id_str)

                    # 更新帖子的状态：保存新的评论数和最新回复时间，以及所有已处理的评论ID
                    processed_posts[post_url] = {
                        "last_comment_count": current_comment_count,
                        "last_comment_datetime": datetime_attr,  # 保存原始字符串，方便 load_state 时直接使用
                        "processed_comment_ids": list(current_post_processed_comment_ids)
                    }

                except Exception as e:
                    print(f"   ⚠️ 处理帖子详情页 {post_url} 时发生错误: {e}")
                finally:
                    post_detail_page.close()  # 关闭当前帖子的页面

            # 短暂等待，避免请求过快
            time.sleep(1)

    except Exception as e:
        print(f"❌ 爬虫运行时发生错误: {e}")
    finally:
        print("\n--- 爬虫运行结束 ---")
        save_state({"processed_posts": processed_posts})  # 保存最新状态
        save_results(all_flash_sales_found_this_run)  # 保存本次运行发现的闪购信息
        page.close()  # 关闭主页面浏览器
        print(f"找到的闪购信息已保存到: {RESULTS_FILE}")
        print(f"爬取状态已保存到: {STATE_FILE}")


# --- 运行爬虫 ---
if __name__ == "__main__":
    lowendtalk_spider()