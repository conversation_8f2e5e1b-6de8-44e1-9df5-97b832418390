"""
Improved Multi-threaded Forum Crawler for LowEndTalk Flash Sales
Author: AI Assistant
Date: 2025-07-14

This script provides a robust, secure, and maintainable solution for monitoring
LowEndTalk forums for flash sale announcements with comprehensive error handling,
logging, configuration management, and anti-detection measures.
"""

import asyncio
import json
import re
import time
import threading
import queue
import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
from concurrent.futures import ThreadPoolExecutor
import signal
import sys

from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings


@dataclass
class CrawlerConfig:
    """Configuration class for the forum crawler"""
    base_url: str = "https://lowendtalk.com/"
    monitor_url: str = "https://lowendtalk.com"
    state_file: str = "lowendtalk_crawl_state.json"
    results_file: str = "lowendtalk_flash_sales.json"
    
    # Flash sale detection keywords
    flash_sale_keywords: List[str] = field(default_factory=lambda: [
        "offer", "sale", "discount", "promo", "limited", "flash sale",
        "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
        "$/month", "$/yr", "$/year", "$/mo", "price", "deal", "special", "promotion"
    ])
    
    # Threading configuration
    num_workers: int = 5
    monitor_timeout: int = 90
    worker_timeout: int = 30
    queue_timeout: int = 30
    
    # Browser configuration
    headless: bool = True
    user_agents: List[str] = field(default_factory=lambda: [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ])
    
    # Timing configuration
    refresh_interval: int = 60
    scroll_delay: float = 2.0
    page_load_delay: float = 3.0
    
    # Rate limiting
    min_request_delay: float = 1.0
    max_request_delay: float = 3.0
    
    # Monitoring configuration
    max_posts_to_check: int = 10
    max_retries: int = 3
    
    @classmethod
    def from_file(cls, config_path: str) -> 'CrawlerConfig':
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        except FileNotFoundError:
            logging.warning(f"Configuration file not found: {config_path}, using defaults")
            return cls()
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in configuration file: {e}")
            return cls()


class CrawlerLogger:
    """Enhanced logging system for the crawler"""
    
    @staticmethod
    def setup_logging(log_level: str = "INFO", log_file: str = "forum_crawler.log") -> logging.Logger:
        """Setup comprehensive logging configuration"""
        logger = logging.getLogger("forum_crawler")
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger


class AntiDetectionManager:
    """Manages anti-detection measures"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.last_request_time = 0
        self.request_count = 0
        
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.config.user_agents)
    
    def get_random_delay(self) -> float:
        """Get a random delay between requests"""
        return random.uniform(
            self.config.min_request_delay,
            self.config.max_request_delay
        )
    
    async def apply_rate_limiting(self) -> None:
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        min_delay = self.get_random_delay()
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def should_take_break(self) -> bool:
        """Determine if we should take a longer break"""
        # Take a break every 50 requests
        return self.request_count > 0 and self.request_count % 50 == 0
    
    async def take_break(self) -> None:
        """Take a longer break to avoid detection"""
        break_time = random.uniform(30, 120)  # 30-120 seconds
        logging.info(f"Taking anti-detection break for {break_time:.1f} seconds")
        await asyncio.sleep(break_time)


class StateManager:
    """Thread-safe state management"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.lock = threading.RLock()
        self.logger = logging.getLogger("forum_crawler.state")
    
    def load_state(self) -> Dict[str, Any]:
        """Load crawler state from file"""
        with self.lock:
            try:
                if Path(self.config.state_file).exists():
                    with open(self.config.state_file, 'r', encoding='utf-8') as f:
                        state = json.load(f)
                    self.logger.debug(f"Loaded state with {len(state.get('processed_posts', {}))} processed posts")
                    return state
                return {"processed_posts": {}, "last_run": None}
            except Exception as e:
                self.logger.error(f"Failed to load state file: {e}")
                return {"processed_posts": {}, "last_run": None}
    
    def save_state(self, state: Dict[str, Any]) -> None:
        """Save crawler state to file"""
        with self.lock:
            try:
                state["last_run"] = datetime.now(timezone.utc).isoformat()
                with open(self.config.state_file, 'w', encoding='utf-8') as f:
                    json.dump(state, f, ensure_ascii=False, indent=2)
                self.logger.debug("State saved successfully")
            except Exception as e:
                self.logger.error(f"Failed to save state file: {e}")
    
    def load_results(self) -> List[Dict[str, Any]]:
        """Load previous flash sale results"""
        with self.lock:
            try:
                if Path(self.config.results_file).exists():
                    with open(self.config.results_file, 'r', encoding='utf-8') as f:
                        results = json.load(f)
                    self.logger.debug(f"Loaded {len(results)} previous results")
                    return results
                return []
            except Exception as e:
                self.logger.error(f"Failed to load results file: {e}")
                return []
    
    def save_results(self, new_results: List[Dict[str, Any]]) -> None:
        """Save flash sale results with deduplication"""
        with self.lock:
            if not new_results:
                self.logger.info("No new results to save")
                return
            
            existing_results = self.load_results()
            
            # Create hash set for deduplication
            existing_hashes = {
                self._hash_result(result) for result in existing_results
            }
            
            new_items_added = 0
            for result in new_results:
                result_hash = self._hash_result(result)
                if result_hash not in existing_hashes:
                    existing_results.append(result)
                    existing_hashes.add(result_hash)
                    new_items_added += 1
            
            if new_items_added > 0:
                try:
                    with open(self.config.results_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_results, f, ensure_ascii=False, indent=2)
                    self.logger.info(f"Saved {new_items_added} new flash sale results")
                except Exception as e:
                    self.logger.error(f"Failed to save results: {e}")
            else:
                self.logger.info("No new unique results to save")
    
    def _hash_result(self, result: Dict[str, Any]) -> str:
        """Create a hash for result deduplication"""
        # Use post_url and comment_id for uniqueness
        key = f"{result.get('post_url', '')}-{result.get('comment_id', '')}"
        return hashlib.md5(key.encode()).hexdigest()


class FlashSaleDetector:
    """Enhanced flash sale detection with improved accuracy"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.detector")
        
        # Compile regex patterns for better performance
        self.price_patterns = [
            re.compile(r'\$\d+(?:\.\d{2})?(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'\d+(?:\.\d{2})?\s*(?:usd|eur|gbp)(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'(?:from|starting|only)\s*\$?\d+', re.IGNORECASE)
        ]
        
        self.spec_patterns = [
            re.compile(r'\d+\s*(?:gb|tb|mb)\s*(?:ram|memory|storage|disk)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:core|cpu|vcpu)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:gb|tb)\s*(?:bandwidth|traffic)', re.IGNORECASE)
        ]
    
    def is_flash_sale(self, text: str) -> Dict[str, Any]:
        """Enhanced flash sale detection with confidence scoring"""
        if not text or len(text.strip()) < 10:
            return {"is_flash_sale": False, "confidence": 0.0, "reasons": []}
        
        text_lower = text.lower()
        reasons = []
        confidence = 0.0
        
        # Check for flash sale keywords
        keyword_matches = 0
        for keyword in self.config.flash_sale_keywords:
            if keyword in text_lower:
                keyword_matches += 1
                reasons.append(f"keyword: {keyword}")
        
        if keyword_matches > 0:
            confidence += min(keyword_matches * 0.1, 0.5)
        
        # Check for price patterns
        price_matches = 0
        for pattern in self.price_patterns:
            if pattern.search(text):
                price_matches += 1
                reasons.append("price_pattern")
        
        if price_matches > 0:
            confidence += min(price_matches * 0.2, 0.4)
        
        # Check for specification patterns
        spec_matches = 0
        for pattern in self.spec_patterns:
            if pattern.search(text):
                spec_matches += 1
                reasons.append("spec_pattern")
        
        if spec_matches > 0:
            confidence += min(spec_matches * 0.1, 0.3)
        
        # Boost confidence for certain combinations
        if "flash" in text_lower and "sale" in text_lower:
            confidence += 0.3
            reasons.append("flash_sale_combo")
        
        if "limited" in text_lower and ("time" in text_lower or "offer" in text_lower):
            confidence += 0.2
            reasons.append("limited_time_offer")
        
        # Reduce confidence for certain patterns that might be false positives
        if "sold out" in text_lower or "expired" in text_lower:
            confidence *= 0.5
            reasons.append("negative_indicator")
        
        is_flash_sale = confidence >= 0.3  # Threshold for classification
        
        return {
            "is_flash_sale": is_flash_sale,
            "confidence": min(confidence, 1.0),
            "reasons": reasons,
            "keyword_matches": keyword_matches,
            "price_matches": price_matches,
            "spec_matches": spec_matches
        }


class TimeUtils:
    """Utility functions for time parsing and handling"""
    
    @staticmethod
    def parse_time_string(time_str: Optional[str]) -> Optional[datetime]:
        """Parse various time string formats to datetime objects"""
        if not time_str:
            return None
        
        try:
            # Handle ISO format
            dt_obj = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            if dt_obj.tzinfo is None:
                return dt_obj.replace(tzinfo=timezone.utc)
            return dt_obj
        except ValueError:
            pass
        
        # Handle relative formats like "July 13"
        current_year = datetime.now().year
        formats_to_try = [
            f"%B %d, {current_year}",
            f"%B %d {current_year}",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d"
        ]
        
        for fmt in formats_to_try:
            try:
                return datetime.strptime(time_str, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue
        
        logging.warning(f"Could not parse time string: {time_str}")
        return None


class BrowserManager:
    """Manages browser instances and tabs with proper resource cleanup"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.browser = None
        self.logger = logging.getLogger("forum_crawler.browser")
        self.anti_detection = AntiDetectionManager(config)
    
    def __enter__(self):
        """Context manager entry"""
        self.start_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.stop_browser()
    
    def start_browser(self) -> None:
        """Initialize browser with anti-detection settings"""
        try:
            # Configure DrissionPage settings
            Settings.set_singleton_tab_obj(False)

            # Create browser with proper DrissionPage API
            # Note: DrissionPage Chromium class doesn't accept these options directly
            # We need to configure them through ChromiumOptions if needed

            self.browser = Chromium()
            self.logger.info("Browser started successfully")

            # Apply anti-detection settings after browser creation
            if hasattr(self.browser, 'set_user_agent'):
                self.browser.set_user_agent(self.anti_detection.get_random_user_agent())

        except Exception as e:
            self.logger.error(f"Failed to start browser: {e}")
            raise
    
    def stop_browser(self) -> None:
        """Safely stop browser and cleanup resources"""
        if self.browser:
            try:
                self.browser.quit()
                self.logger.info("Browser stopped successfully")
            except Exception as e:
                self.logger.error(f"Error stopping browser: {e}")
    
    def create_tab(self) -> Optional[ChromiumPage]:
        """Create a new browser tab with error handling"""
        try:
            if not self.browser:
                raise RuntimeError("Browser not initialized")

            tab = self.browser.new_tab()

            # Apply anti-detection measures if the method exists
            try:
                if hasattr(tab, 'set') and hasattr(tab.set, 'user_agent'):
                    tab.set.user_agent(self.anti_detection.get_random_user_agent())
                elif hasattr(tab, 'set_user_agent'):
                    tab.set_user_agent(self.anti_detection.get_random_user_agent())
            except Exception as ua_error:
                self.logger.warning(f"Could not set user agent: {ua_error}")

            return tab
        except Exception as e:
            self.logger.error(f"Failed to create browser tab: {e}")
            return None


class HealthMonitor:
    """Monitors the health of crawler components"""
    
    def __init__(self):
        self.thread_health = {}
        self.last_activity = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger("forum_crawler.health")
    
    def update_thread_health(self, thread_id: str, status: str, details: str = "") -> None:
        """Update thread health status"""
        with self.lock:
            self.thread_health[thread_id] = {
                "status": status,
                "details": details,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.last_activity[thread_id] = time.time()
    
    def check_thread_health(self, max_inactive_time: int = 300) -> Dict[str, Any]:
        """Check health of all threads"""
        with self.lock:
            current_time = time.time()
            health_report = {
                "healthy_threads": [],
                "unhealthy_threads": [],
                "inactive_threads": []
            }
            
            for thread_id, last_time in self.last_activity.items():
                time_since_activity = current_time - last_time
                thread_status = self.thread_health.get(thread_id, {})
                
                if time_since_activity > max_inactive_time:
                    health_report["inactive_threads"].append({
                        "thread_id": thread_id,
                        "inactive_time": time_since_activity,
                        "last_status": thread_status
                    })
                elif thread_status.get("status") == "error":
                    health_report["unhealthy_threads"].append({
                        "thread_id": thread_id,
                        "status": thread_status
                    })
                else:
                    health_report["healthy_threads"].append(thread_id)
            
            return health_report
    
    def log_health_summary(self) -> None:
        """Log a summary of system health"""
        health = self.check_thread_health()
        self.logger.info(f"Health Summary - Healthy: {len(health['healthy_threads'])}, "
                        f"Unhealthy: {len(health['unhealthy_threads'])}, "
                        f"Inactive: {len(health['inactive_threads'])}")


# Global instances for shared resources
health_monitor = HealthMonitor()
shutdown_event = threading.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logging.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_event.set()


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


class ForumCrawler:
    """Main crawler class orchestrating all components"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.main")
        self.state_manager = StateManager(config)
        self.detector = FlashSaleDetector(config)
        
        # Thread synchronization
        self.task_queue = queue.Queue()
        self.monitor_ready_event = threading.Event()
        self.all_flash_sales_found = []
        self.results_lock = threading.Lock()
        
        # Thread management
        self.worker_threads = []
        self.monitor_thread = None
        
    def start(self) -> None:
        """Start the crawler with all components"""
        self.logger.info("Starting Forum Crawler...")
        
        try:
            with BrowserManager(self.config) as browser_manager:
                self._start_threads(browser_manager)
                self._run_main_loop()
        except Exception as e:
            self.logger.error(f"Critical error in crawler: {e}")
        finally:
            self._cleanup()
    
    def _start_threads(self, browser_manager: BrowserManager) -> None:
        """Start monitor and worker threads"""
        # Start monitor thread
        self.monitor_thread = threading.Thread(
            target=self._monitor_thread_func,
            args=(browser_manager,),
            name="Monitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        # Start worker threads
        for i in range(self.config.num_workers):
            worker_thread = threading.Thread(
                target=self._worker_thread_func,
                args=(f"Worker-{i+1}", browser_manager),
                name=f"Worker-{i+1}",
                daemon=True
            )
            worker_thread.start()
            self.worker_threads.append(worker_thread)
        
        self.logger.info(f"Started {self.config.num_workers} worker threads and 1 monitor thread")
    
    def _run_main_loop(self) -> None:
        """Main loop for monitoring and health checks"""
        health_check_interval = 60  # Check health every minute
        last_health_check = 0
        
        while not shutdown_event.is_set():
            try:
                current_time = time.time()
                
                # Periodic health checks
                if current_time - last_health_check > health_check_interval:
                    health_monitor.log_health_summary()
                    last_health_check = current_time
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt")
                break
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                time.sleep(5)
    
    def _cleanup(self) -> None:
        """Cleanup resources and save final state"""
        self.logger.info("Starting cleanup process...")
        
        # Signal shutdown to all threads
        shutdown_event.set()
        
        # Wait for threads to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=10)
        
        # Save final results
        with self.results_lock:
            if self.all_flash_sales_found:
                self.state_manager.save_results(self.all_flash_sales_found)
                self.logger.info(f"Saved {len(self.all_flash_sales_found)} flash sale results")
        
        # Save final state
        final_state = self.state_manager.load_state()
        self.state_manager.save_state(final_state)
        
        self.logger.info("Cleanup completed")
    
    def _monitor_thread_func(self, browser_manager: BrowserManager) -> None:
        """Monitor thread function - tracks forum for updates"""
        thread_id = "monitor"
        health_monitor.update_thread_health(thread_id, "starting", "Initializing monitor thread")

        try:
            monitor_page = browser_manager.create_tab()
            if not monitor_page:
                raise RuntimeError("Failed to create monitor page")

            # Perform initial scan with timeout protection
            try:
                self._perform_initial_scan(monitor_page, thread_id)
                self.monitor_ready_event.set()
                self.logger.info("Monitor thread ready event set - workers can now start")
            except Exception as e:
                self.logger.error(f"Initial scan failed: {e}")
                # Set the event anyway to prevent workers from hanging
                self.monitor_ready_event.set()
                raise

            # Continuous monitoring loop
            while not shutdown_event.is_set():
                try:
                    self._perform_monitoring_cycle(monitor_page, thread_id)
                    time.sleep(self.config.refresh_interval)
                except Exception as e:
                    self.logger.error(f"Error in monitoring cycle: {e}")
                    health_monitor.update_thread_health(thread_id, "error", str(e))
                    time.sleep(30)  # Wait before retrying

        except Exception as e:
            self.logger.error(f"Critical error in monitor thread: {e}")
            health_monitor.update_thread_health(thread_id, "critical_error", str(e))
            # Always set the ready event to prevent workers from hanging
            self.monitor_ready_event.set()
        finally:
            health_monitor.update_thread_health(thread_id, "stopped", "Monitor thread ended")
            self.logger.info("Monitor thread ended")
    
    def _perform_initial_scan(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform initial scan of the forum"""
        health_monitor.update_thread_health(thread_id, "initial_scan", "Loading forum page")

        try:
            self.logger.info(f"Loading forum page: {self.config.monitor_url}")
            monitor_page.get(self.config.monitor_url)

            # Wait for page to load
            self.logger.info("Waiting for page to load...")
            time.sleep(self.config.page_load_delay)

            # Try multiple selectors to find posts container
            posts_container = None
            selectors_to_try = [
                'tag:ul@class=DataList Discussions',
                '.DataList.Discussions',
                'ul.DataList.Discussions',
                '.ItemDiscussion'  # Try to find individual posts if container not found
            ]

            for selector in selectors_to_try:
                self.logger.info(f"Trying selector: {selector}")
                try:
                    posts_container = monitor_page.ele(selector, timeout=10)
                    if posts_container:
                        self.logger.info(f"Found posts container with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not posts_container:
                # Log page content for debugging
                self.logger.error("Could not find posts container. Page title: " +
                                (monitor_page.title if hasattr(monitor_page, 'title') else "Unknown"))

                # Try a more basic approach - just look for any posts
                try:
                    # Check if we can find any discussion items at all
                    any_posts = monitor_page.eles('.ItemDiscussion')
                    if any_posts:
                        self.logger.info(f"Found {len(any_posts)} individual posts, proceeding without container")
                        # Create a mock container for processing
                        posts_container = monitor_page  # Use the page itself as container
                    else:
                        raise RuntimeError("No posts found on page - site may be down or structure changed")
                except Exception as e:
                    self.logger.error(f"Failed to find any posts: {e}")
                    raise RuntimeError("Could not find any posts on the forum page")

            health_monitor.update_thread_health(thread_id, "scanning", "Processing initial posts")
            self._process_posts(posts_container, thread_id, is_initial=True)

            health_monitor.update_thread_health(thread_id, "ready", "Initial scan completed")
            self.logger.info("Initial scan completed, monitor thread ready")

        except Exception as e:
            self.logger.error(f"Error in initial scan: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Initial scan failed: {e}")
            raise
    
    def _perform_monitoring_cycle(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform a single monitoring cycle"""
        health_monitor.update_thread_health(thread_id, "refreshing", "Refreshing forum page")
        
        monitor_page.refresh()
        time.sleep(self.config.page_load_delay)
        
        posts_container = monitor_page.ele('tag:ul@class=DataList Discussions', timeout=30)
        if not posts_container:
            self.logger.warning("Could not find posts container after refresh")
            return
        
        health_monitor.update_thread_health(thread_id, "processing", "Processing updated posts")
        self._process_posts(posts_container, thread_id, is_initial=False)
        
        health_monitor.update_thread_health(thread_id, "active", "Monitoring cycle completed")
    
    def _process_posts(self, posts_container, thread_id: str, is_initial: bool = False) -> None:
        """Process posts from the container"""
        try:
            # Get all discussion items - try multiple approaches
            all_items = []

            # Try different selectors to find discussion items
            # Based on debug analysis: actual classes are "Item Unread ItemDiscussion ItemDiscussion-withPhoto"
            selectors_to_try = [
                'xpath:./li[contains(@class, "Item")]',  # Direct children with Item class
                'li.Item',  # Li elements with Item class
                'xpath:.//li[contains(@class, "ItemDiscussion")]',  # Fallback to original
                '.Item'  # Any element with Item class
            ]

            for selector in selectors_to_try:
                try:
                    items = posts_container.eles(selector)
                    if items:
                        all_items = items
                        self.logger.info(f"Found {len(all_items)} items using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not all_items:
                self.logger.warning("No discussion items found, trying fallback approach")
                # Fallback: try to find any items that might be posts
                try:
                    all_items = posts_container.eles('li')
                    self.logger.info(f"Fallback found {len(all_items)} li elements")
                except Exception as e:
                    self.logger.error(f"Fallback approach failed: {e}")
                    return

            # Filter out announcements - use faster approach
            non_announcement_posts = []
            for i, item in enumerate(all_items):
                try:
                    # Use a faster method - check if class contains announcement
                    item_classes = item.attr('class') or ''
                    if 'announcement' not in item_classes.lower():
                        non_announcement_posts.append(item)
                        if i < 5:  # Log first few for debugging
                            self.logger.debug(f"Item {i+1} classes: {item_classes}")
                    else:
                        self.logger.debug(f"Skipping announcement item {i+1}")
                except Exception as e:
                    # If we can't check for announcements, include the item anyway
                    self.logger.warning(f"Could not check announcement status for item {i+1}: {e}")
                    non_announcement_posts.append(item)

            # Limit posts to check
            posts_to_check = non_announcement_posts[:self.config.max_posts_to_check]

            scan_type = "Initial" if is_initial else "Update"
            self.logger.info(f"{scan_type} scan found {len(posts_to_check)} posts to check")

            if not posts_to_check:
                self.logger.warning("No posts to check after filtering")
                return

            # Load current state
            self.logger.info("Loading current state...")
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            self.logger.info(f"Loaded state with {len(processed_posts)} previously processed posts")

            for i, item in enumerate(posts_to_check):
                try:
                    self.logger.info(f"Processing post {i+1}/{len(posts_to_check)}")
                    post_data = self._extract_post_data(item)
                    if not post_data:
                        self.logger.warning(f"Could not extract data from post {i+1}")
                        continue

                    self.logger.info(f"Extracted post: {post_data['post_title'][:50]}...")
                    should_queue = self._should_process_post(post_data, processed_posts)

                    if should_queue:
                        self.task_queue.put(post_data)
                        self.logger.info(f"Queued post: {post_data['post_title'][:50]}...")
                    else:
                        # Update state for unchanged posts
                        self._update_post_state(post_data, processed_posts)
                        self.logger.debug(f"Skipped unchanged post: {post_data['post_title'][:50]}...")

                except Exception as e:
                    self.logger.error(f"Error processing post item {i+1}: {e}")
                    continue

            # Save updated state
            self.logger.info("Saving updated state...")
            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)
            self.logger.info(f"Post processing completed for {scan_type} scan")

        except Exception as e:
            self.logger.error(f"Error in _process_posts: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Post processing error: {e}")
            raise  # Re-raise to ensure the error is handled properly

    def _extract_post_data(self, item) -> Optional[Dict[str, Any]]:
        """Extract post data from a forum item"""
        try:
            # Use the working approach from quick_fix_crawler.py
            # Try to find title link directly (this works!)
            title_link = item.ele('a', timeout=2)
            if not title_link:
                self.logger.debug("No title link found")
                return None

            post_title = title_link.text.strip()
            post_url = title_link.link

            if not post_title or not post_url:
                self.logger.debug("Missing title or URL")
                return None

            # Ensure absolute URL
            if not post_url.startswith('http'):
                post_url = self.config.base_url.rstrip('/') + post_url

            # Extract comment count using the working method
            current_comment_count = 0
            try:
                # Look for comment count in various places
                count_elements = item.eles('.Number')
                for elem in count_elements:
                    text = elem.text.strip()
                    if text.isdigit():
                        current_comment_count = int(text)
                        break
                    elif 'k' in text.lower():
                        try:
                            num = float(text.lower().replace('k', ''))
                            current_comment_count = int(num * 1000)
                            break
                        except:
                            pass
            except Exception as e:
                self.logger.debug(f"Could not extract comment count: {e}")

            # Extract last comment date - simplified approach
            datetime_attr = None
            try:
                time_elements = item.eles('tag:time')
                if time_elements:
                    datetime_attr = time_elements[0].attr('datetime')
            except Exception as e:
                self.logger.debug(f"Could not extract datetime: {e}")

            # If we got here, we have at least title and URL
            # Use safe logging to avoid Unicode errors
            safe_title = post_title[:50].encode('ascii', 'ignore').decode('ascii')
            self.logger.info(f"Extracted: {safe_title}... (Comments: {current_comment_count})")

            return {
                "post_url": post_url,
                "post_title": post_title,
                "current_comment_count": current_comment_count,
                "datetime_attr": datetime_attr or datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error extracting post data: {e}")
            return None

    def _should_process_post(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> bool:
        """Determine if a post should be processed"""
        post_url = post_data["post_url"]
        current_comment_count = post_data["current_comment_count"]
        datetime_attr = post_data["datetime_attr"]

        if post_url not in processed_posts:
            self.logger.debug(f"New post detected: {post_data['post_title'][:50]}...")
            return True

        prev_state = processed_posts[post_url]
        prev_comment_count = prev_state.get("last_comment_count", 0)
        prev_datetime_str = prev_state.get("last_comment_datetime")

        # Check comment count increase
        if current_comment_count > prev_comment_count:
            self.logger.debug(f"Comment count increased: {prev_comment_count} -> {current_comment_count}")
            return True

        # Check timestamp update
        if datetime_attr and prev_datetime_str:
            current_datetime = TimeUtils.parse_time_string(datetime_attr)
            prev_datetime = TimeUtils.parse_time_string(prev_datetime_str)

            if current_datetime and prev_datetime and current_datetime > prev_datetime:
                self.logger.debug(f"Timestamp updated: {prev_datetime_str} -> {datetime_attr}")
                return True

        return False

    def _update_post_state(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> None:
        """Update state for posts that don't need processing"""
        post_url = post_data["post_url"]
        prev_state = processed_posts.get(post_url, {})

        processed_posts[post_url] = {
            "last_comment_count": post_data["current_comment_count"],
            "last_comment_datetime": post_data["datetime_attr"],
            "processed_comment_ids": prev_state.get("processed_comment_ids", [])
        }

    def _worker_thread_func(self, worker_id: str, browser_manager: BrowserManager) -> None:
        """Worker thread function - processes posts from the queue"""
        health_monitor.update_thread_health(worker_id, "starting", "Initializing worker thread")

        try:
            worker_page = browser_manager.create_tab()
            if not worker_page:
                raise RuntimeError("Failed to create worker page")

            # Wait for monitor to be ready
            self.logger.info(f"[{worker_id}] Waiting for monitor thread...")
            if not self.monitor_ready_event.wait(timeout=self.config.monitor_timeout):
                self.logger.warning(f"[{worker_id}] Monitor thread not ready, worker exiting")
                return

            health_monitor.update_thread_health(worker_id, "ready", "Worker ready to process tasks")

            # Main processing loop
            while not shutdown_event.is_set():
                try:
                    # Get task from queue
                    task_data = self.task_queue.get(timeout=self.config.queue_timeout)

                    health_monitor.update_thread_health(worker_id, "processing", f"Processing: {task_data['post_title'][:30]}...")

                    # Process the post
                    self._process_post_comments(worker_id, worker_page, task_data)

                    self.task_queue.task_done()
                    health_monitor.update_thread_health(worker_id, "active", "Task completed")

                except queue.Empty:
                    # No tasks available, continue waiting
                    health_monitor.update_thread_health(worker_id, "waiting", "Waiting for tasks")
                    continue
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing task: {e}")
                    health_monitor.update_thread_health(worker_id, "error", str(e))

                    # Try to recover by recreating the page
                    try:
                        worker_page.close()
                        worker_page = browser_manager.create_tab()
                    except Exception as recovery_error:
                        self.logger.error(f"[{worker_id}] Failed to recover: {recovery_error}")
                        break

                    self.task_queue.task_done()

        except Exception as e:
            self.logger.error(f"[{worker_id}] Critical error in worker thread: {e}")
            health_monitor.update_thread_health(worker_id, "critical_error", str(e))
        finally:
            health_monitor.update_thread_health(worker_id, "stopped", "Worker thread ended")
            self.logger.info(f"[{worker_id}] Worker thread ended")

    def _process_post_comments(self, worker_id: str, worker_page: ChromiumPage, task_data: Dict[str, Any]) -> None:
        """Process comments in a post for flash sales"""
        post_url = task_data["post_url"]
        post_title = task_data["post_title"]
        current_comment_count = task_data["current_comment_count"]
        datetime_attr = task_data["datetime_attr"]

        self.logger.info(f"[{worker_id}] Processing: {post_title}")

        try:
            # Navigate to post
            worker_page.get(post_url)
            worker_page.wait.load_start()

            # Apply anti-detection delay
            time.sleep(random.uniform(1, 3))

            # Scroll to load all comments
            self._scroll_to_load_comments(worker_page, worker_id)

            # Get all comments
            comments = worker_page.eles('.Comment')
            self.logger.info(f"[{worker_id}] Found {len(comments)} comments")

            # Get previously processed comment IDs
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            prev_processed_ids = set(processed_posts.get(post_url, {}).get("processed_comment_ids", []))

            new_processed_ids = set()
            flash_sales_found = []

            # Process each comment
            for comment_element in comments:
                try:
                    comment_id = comment_element.attr('id')
                    if not comment_id or comment_id in prev_processed_ids:
                        continue

                    comment_text_element = comment_element.ele('.Message')
                    if not comment_text_element:
                        continue

                    comment_text = comment_text_element.text.strip()
                    if len(comment_text) < 10:  # Skip very short comments
                        continue

                    # Check for flash sale
                    detection_result = self.detector.is_flash_sale(comment_text)

                    if detection_result["is_flash_sale"]:
                        flash_sale_info = {
                            "post_title": post_title,
                            "post_url": post_url,
                            "comment_id": comment_id,
                            "comment_text": comment_text,
                            "confidence": detection_result["confidence"],
                            "detection_reasons": detection_result["reasons"],
                            "crawled_time": datetime.now(timezone.utc).isoformat(),
                            "worker_id": worker_id
                        }

                        flash_sales_found.append(flash_sale_info)

                        self.logger.info(f"[{worker_id}] 🎉 Flash sale detected! "
                                       f"Confidence: {detection_result['confidence']:.2f}, "
                                       f"Comment ID: {comment_id}")
                        self.logger.debug(f"[{worker_id}] Content: {comment_text[:100]}...")

                    new_processed_ids.add(comment_id)

                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing comment: {e}")
                    continue

            # Update results and state
            if flash_sales_found:
                with self.results_lock:
                    self.all_flash_sales_found.extend(flash_sales_found)

                self.logger.info(f"[{worker_id}] Found {len(flash_sales_found)} flash sales in this post")

            # Update post state
            self._update_processed_post_state(post_url, current_comment_count, datetime_attr,
                                            prev_processed_ids.union(new_processed_ids))

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error processing post comments: {e}")
            raise

    def _scroll_to_load_comments(self, page: ChromiumPage, worker_id: str) -> None:
        """Scroll page to load all comments"""
        try:
            last_height = page.scroll.to_bottom()
            scroll_attempts = 0
            max_scroll_attempts = 10

            while scroll_attempts < max_scroll_attempts:
                time.sleep(self.config.scroll_delay)
                new_height = page.scroll.to_bottom()

                if new_height == last_height:
                    break  # No more content to load

                last_height = new_height
                scroll_attempts += 1

                self.logger.debug(f"[{worker_id}] Scrolled to load more comments (attempt {scroll_attempts})")

            if scroll_attempts >= max_scroll_attempts:
                self.logger.warning(f"[{worker_id}] Reached maximum scroll attempts")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error during scrolling: {e}")

    def _update_processed_post_state(self, post_url: str, comment_count: int,
                                   datetime_attr: str, processed_ids: Set[str]) -> None:
        """Update the processed state for a post"""
        try:
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})

            processed_posts[post_url] = {
                "last_comment_count": comment_count,
                "last_comment_datetime": datetime_attr,
                "processed_comment_ids": list(processed_ids)
            }

            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)

        except Exception as e:
            self.logger.error(f"Error updating post state: {e}")


def main():
    """Main function to run the improved forum crawler"""
    # Setup logging
    logger = CrawlerLogger.setup_logging("INFO", "forum_crawler.log")

    try:
        # Load configuration
        config = CrawlerConfig.from_file("crawler_config.json")

        logger.info("Starting Improved Forum Crawler")
        logger.info(f"Configuration: {config.num_workers} workers, "
                   f"{config.refresh_interval}s refresh interval")

        # Create and start crawler
        crawler = ForumCrawler(config)
        crawler.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Critical error in main: {e}")
    finally:
        logger.info("Forum crawler shutdown complete")


if __name__ == "__main__":
    main()
