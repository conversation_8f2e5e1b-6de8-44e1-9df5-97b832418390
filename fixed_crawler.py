"""
Fixed version of the forum crawler addressing Unicode, data extraction, and logic issues
"""

import time
import logging
import json
import threading
import queue
import re
from datetime import datetime, timezone
from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# Setup logging with UTF-8 encoding
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('crawler.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Global variables
task_queue = queue.Queue()
results = []
results_lock = threading.Lock()
monitor_ready = threading.Event()
shutdown_event = threading.Event()

def safe_log(text, max_length=100):
    """Safe logging to avoid Unicode errors"""
    try:
        return text[:max_length]
    except:
        return text.encode('ascii', 'ignore').decode('ascii')[:max_length]

def extract_post_data(item):
    """Extract post data with correct selectors for comments vs views"""
    try:
        # Find title link
        title_link = item.ele('a', timeout=2)
        if not title_link:
            return None
        
        title = title_link.text.strip()
        url = title_link.link
        
        if not title or not url:
            return None
        
        # Ensure absolute URL
        if not url.startswith('http'):
            url = 'https://lowendtalk.com' + url
        
        # Extract comment count (NOT views) - this was the bug!
        comment_count = 0
        try:
            # Look for the specific comment count element
            # The structure is: .Meta .MItem.MCount.CommentCount .Number
            meta_elements = item.eles('.Meta')
            for meta in meta_elements:
                comment_elements = meta.eles('.MItem.MCount.CommentCount')
                if comment_elements:
                    number_elem = comment_elements[0].ele('.Number')
                    if number_elem:
                        count_text = number_elem.text.strip()
                        logger.debug(f"Found comment count text: '{count_text}'")
                        
                        # Parse the count
                        if 'k' in count_text.lower():
                            try:
                                num = float(count_text.lower().replace('k', ''))
                                comment_count = int(num * 1000)
                            except:
                                pass
                        elif count_text.isdigit():
                            comment_count = int(count_text)
                        break
        except Exception as e:
            logger.debug(f"Could not extract comment count: {e}")
        
        # Extract views count for comparison (to verify we're getting the right data)
        views_count = 0
        try:
            meta_elements = item.eles('.Meta')
            for meta in meta_elements:
                view_elements = meta.eles('.MItem.MCount.ViewCount')
                if view_elements:
                    number_elem = view_elements[0].ele('.Number')
                    if number_elem:
                        views_text = number_elem.text.strip()
                        if 'k' in views_text.lower():
                            try:
                                num = float(views_text.lower().replace('k', ''))
                                views_count = int(num * 1000)
                            except:
                                pass
                        elif views_text.isdigit():
                            views_count = int(views_text)
                        break
        except Exception as e:
            logger.debug(f"Could not extract views count: {e}")
        
        logger.info(f"Extracted: {safe_log(title)} (Comments: {comment_count}, Views: {views_count})")
        
        return {
            "post_url": url,
            "post_title": title,
            "current_comment_count": comment_count,
            "views_count": views_count,  # For debugging
            "datetime_attr": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error extracting post data: {e}")
        return None

def should_process_post(post_data, processed_posts):
    """Determine if a post should be processed"""
    post_url = post_data["post_url"]
    current_comment_count = post_data["current_comment_count"]
    
    # Always process if it's a new post
    if post_url not in processed_posts:
        logger.info(f"New post detected: {safe_log(post_data['post_title'])}")
        return True
    
    # Check if comment count increased
    prev_state = processed_posts[post_url]
    prev_comment_count = prev_state.get("last_comment_count", 0)
    
    if current_comment_count > prev_comment_count:
        logger.info(f"Comment count increased: {prev_comment_count} -> {current_comment_count}")
        return True
    
    # For testing, process posts with high comment counts occasionally
    if current_comment_count > 1000:
        import random
        if random.random() < 0.1:  # 10% chance to reprocess high-activity posts
            logger.info(f"Randomly reprocessing high-activity post: {safe_log(post_data['post_title'])}")
            return True
    
    return False

def load_state():
    """Load crawler state"""
    try:
        with open('crawler_state.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {"processed_posts": {}}
    except Exception as e:
        logger.error(f"Error loading state: {e}")
        return {"processed_posts": {}}

def save_state(state):
    """Save crawler state"""
    try:
        state["last_run"] = datetime.now(timezone.utc).isoformat()
        with open('crawler_state.json', 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"Error saving state: {e}")

def monitor_thread():
    """Monitor thread function"""
    logger.info("Starting monitor thread...")
    
    try:
        Settings.set_singleton_tab_obj(False)
        browser = Chromium()
        page = browser.new_tab()
        
        while not shutdown_event.is_set():
            try:
                # Navigate to forum
                url = "https://lowendtalk.com/categories/offers"
                logger.info(f"Loading: {url}")
                page.get(url)
                time.sleep(3)
                
                # Find posts container
                container = page.ele('tag:ul@class=DataList Discussions', timeout=10)
                if not container:
                    logger.error("Could not find posts container")
                    time.sleep(30)
                    continue
                
                # Get posts
                posts = container.eles('xpath:./li[contains(@class, "Item")]')
                logger.info(f"Found {len(posts)} posts")
                
                # Load current state
                current_state = load_state()
                processed_posts = current_state.get("processed_posts", {})
                
                # Process first 5 posts
                posts_to_process = posts[:5]
                new_tasks = 0
                
                for i, post in enumerate(posts_to_process):
                    try:
                        logger.info(f"Processing post {i+1}/{len(posts_to_process)}")
                        
                        post_data = extract_post_data(post)
                        if not post_data:
                            logger.warning(f"Could not extract data from post {i+1}")
                            continue
                        
                        if should_process_post(post_data, processed_posts):
                            task_queue.put(post_data)
                            new_tasks += 1
                            logger.info(f"Queued: {safe_log(post_data['post_title'])}")
                        
                        # Update state regardless
                        processed_posts[post_data["post_url"]] = {
                            "last_comment_count": post_data["current_comment_count"],
                            "last_check": datetime.now(timezone.utc).isoformat()
                        }
                        
                    except Exception as e:
                        logger.error(f"Error processing post {i+1}: {e}")
                        continue
                
                # Save updated state
                current_state["processed_posts"] = processed_posts
                save_state(current_state)
                
                logger.info(f"Monitor cycle completed. Queued {new_tasks} new tasks.")
                
                # Signal that monitor is ready (first time only)
                if not monitor_ready.is_set():
                    monitor_ready.set()
                    logger.info("Monitor ready - workers can start")
                
                # Wait before next cycle
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in monitor cycle: {e}")
                time.sleep(30)
        
    except Exception as e:
        logger.error(f"Monitor thread error: {e}")
    finally:
        monitor_ready.set()  # Always set the event
        try:
            browser.quit()
        except:
            pass
        logger.info("Monitor thread ended")

def worker_thread(worker_id):
    """Worker thread function"""
    logger.info(f"Worker {worker_id} starting...")
    
    # Wait for monitor to be ready
    if not monitor_ready.wait(timeout=120):
        logger.warning(f"Worker {worker_id}: Monitor not ready, exiting")
        return
    
    try:
        Settings.set_singleton_tab_obj(False)
        browser = Chromium()
        page = browser.new_tab()
        
        while not shutdown_event.is_set():
            try:
                # Get task from queue
                task_data = task_queue.get(timeout=30)
                
                post_url = task_data["post_url"]
                post_title = task_data["post_title"]
                
                logger.info(f"Worker {worker_id}: Processing {safe_log(post_title)}")
                
                # Navigate to post
                page.get(post_url)
                time.sleep(2)
                
                # Scroll to load comments
                page.scroll.to_bottom()
                time.sleep(2)
                
                # Get comments
                comments = page.eles('.Comment')
                logger.info(f"Worker {worker_id}: Found {len(comments)} comments")
                
                # Check for flash sales in comments
                flash_sales_found = []
                keywords = ['sale', 'offer', 'discount', 'promo', '$', 'gb', 'vps', 'hosting']
                
                for j, comment in enumerate(comments[:20]):  # Check first 20 comments
                    try:
                        comment_text = comment.text.strip()
                        if len(comment_text) > 30:  # Skip very short comments
                            # Simple keyword check
                            text_lower = comment_text.lower()
                            keyword_count = sum(1 for keyword in keywords if keyword in text_lower)
                            
                            if keyword_count >= 3:  # At least 3 keywords
                                flash_sale = {
                                    "post_title": post_title,
                                    "post_url": post_url,
                                    "comment_id": f"comment_{j}",
                                    "comment_text": comment_text[:300] + "..." if len(comment_text) > 300 else comment_text,
                                    "keywords_found": keyword_count,
                                    "crawled_time": datetime.now(timezone.utc).isoformat(),
                                    "worker_id": worker_id
                                }
                                flash_sales_found.append(flash_sale)
                                logger.info(f"Worker {worker_id}: 🎉 Flash sale detected! Keywords: {keyword_count}")
                    except Exception as e:
                        logger.debug(f"Error processing comment {j}: {e}")
                
                # Save results
                if flash_sales_found:
                    with results_lock:
                        results.extend(flash_sales_found)
                        # Save to file
                        with open('flash_sales_results.json', 'w', encoding='utf-8') as f:
                            json.dump(results, f, ensure_ascii=False, indent=2)
                        logger.info(f"Worker {worker_id}: Saved {len(flash_sales_found)} flash sales to file")
                
                task_queue.task_done()
                
            except queue.Empty:
                logger.debug(f"Worker {worker_id}: No tasks available")
                continue
            except Exception as e:
                logger.error(f"Worker {worker_id}: Error processing task: {e}")
                try:
                    task_queue.task_done()
                except:
                    pass
                
    except Exception as e:
        logger.error(f"Worker {worker_id}: Critical error: {e}")
    finally:
        try:
            browser.quit()
        except:
            pass
        logger.info(f"Worker {worker_id}: Ended")

def main():
    """Main function"""
    logger.info("Starting Fixed Forum Crawler...")
    
    # Start monitor thread
    monitor = threading.Thread(target=monitor_thread, name="Monitor", daemon=True)
    monitor.start()
    
    # Start 2 worker threads
    workers = []
    for i in range(2):
        worker = threading.Thread(target=worker_thread, args=(i+1,), name=f"Worker-{i+1}", daemon=True)
        worker.start()
        workers.append(worker)
    
    # Main loop
    try:
        while True:
            time.sleep(10)
            logger.info(f"System status - Queue size: {task_queue.qsize()}, Results found: {len(results)}")
            
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        shutdown_event.set()
        
        # Wait for threads
        monitor.join(timeout=10)
        for worker in workers:
            worker.join(timeout=10)
        
        logger.info(f"Crawler stopped. Total flash sales found: {len(results)}")

if __name__ == "__main__":
    main()
