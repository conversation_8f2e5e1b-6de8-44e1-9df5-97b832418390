"""
Comprehensive tests for the improved forum crawler
"""

import pytest
import json
import tempfile
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
import queue

from improved_forum_crawler import (
    CrawlerConfig, StateManager, FlashSaleDetector, 
    AntiDetectionManager, TimeUtils, HealthMonitor,
    ForumCrawler, BrowserManager
)


class TestCrawlerConfig:
    """Test cases for CrawlerConfig"""
    
    def test_default_config(self):
        """Test default configuration creation"""
        config = CrawlerConfig()
        
        assert config.base_url == "https://lowendtalk.com/"
        assert config.num_workers == 5
        assert config.headless is True
        assert len(config.flash_sale_keywords) > 0
        assert len(config.user_agents) > 0
    
    def test_config_from_file(self, tmp_path):
        """Test loading configuration from file"""
        config_data = {
            "base_url": "https://test.com/",
            "num_workers": 3,
            "headless": False
        }
        
        config_file = tmp_path / "test_config.json"
        config_file.write_text(json.dumps(config_data))
        
        config = CrawlerConfig.from_file(str(config_file))
        
        assert config.base_url == "https://test.com/"
        assert config.num_workers == 3
        assert config.headless is False
    
    def test_config_file_not_found(self):
        """Test handling of missing config file"""
        config = CrawlerConfig.from_file("nonexistent.json")
        # Should return default config
        assert config.base_url == "https://lowendtalk.com/"
    
    def test_invalid_json_config(self, tmp_path):
        """Test handling of invalid JSON"""
        config_file = tmp_path / "invalid.json"
        config_file.write_text("invalid json")
        
        config = CrawlerConfig.from_file(str(config_file))
        # Should return default config
        assert config.base_url == "https://lowendtalk.com/"


class TestStateManager:
    """Test cases for StateManager"""
    
    @pytest.fixture
    def temp_config(self, tmp_path):
        """Create temporary config for testing"""
        config = CrawlerConfig()
        config.state_file = str(tmp_path / "test_state.json")
        config.results_file = str(tmp_path / "test_results.json")
        return config
    
    def test_load_empty_state(self, temp_config):
        """Test loading state when no file exists"""
        manager = StateManager(temp_config)
        state = manager.load_state()
        
        assert "processed_posts" in state
        assert state["processed_posts"] == {}
    
    def test_save_and_load_state(self, temp_config):
        """Test saving and loading state"""
        manager = StateManager(temp_config)
        
        test_state = {
            "processed_posts": {
                "http://test.com/post1": {
                    "last_comment_count": 5,
                    "processed_comment_ids": ["comment1", "comment2"]
                }
            }
        }
        
        manager.save_state(test_state)
        loaded_state = manager.load_state()
        
        assert loaded_state["processed_posts"] == test_state["processed_posts"]
        assert "last_run" in loaded_state
    
    def test_save_and_load_results(self, temp_config):
        """Test saving and loading results with deduplication"""
        manager = StateManager(temp_config)
        
        results1 = [
            {"post_url": "http://test.com/post1", "comment_id": "comment1", "text": "sale"},
            {"post_url": "http://test.com/post1", "comment_id": "comment2", "text": "offer"}
        ]
        
        results2 = [
            {"post_url": "http://test.com/post1", "comment_id": "comment1", "text": "sale"},  # Duplicate
            {"post_url": "http://test.com/post2", "comment_id": "comment3", "text": "discount"}  # New
        ]
        
        manager.save_results(results1)
        manager.save_results(results2)
        
        loaded_results = manager.load_results()
        
        # Should have 3 unique results (2 from first batch + 1 new from second)
        assert len(loaded_results) == 3
        
        # Check that all unique items are present
        urls_and_comments = [(r["post_url"], r["comment_id"]) for r in loaded_results]
        expected = [
            ("http://test.com/post1", "comment1"),
            ("http://test.com/post1", "comment2"),
            ("http://test.com/post2", "comment3")
        ]
        
        for item in expected:
            assert item in urls_and_comments


class TestFlashSaleDetector:
    """Test cases for FlashSaleDetector"""
    
    @pytest.fixture
    def detector(self):
        """Create detector instance"""
        config = CrawlerConfig()
        return FlashSaleDetector(config)
    
    def test_obvious_flash_sale(self, detector):
        """Test detection of obvious flash sale"""
        text = "Flash sale! 50% discount on VPS hosting. 4GB RAM, 2 CPU cores, only $5/month!"
        
        result = detector.is_flash_sale(text)
        
        assert result["is_flash_sale"] is True
        assert result["confidence"] > 0.5
        assert "flash_sale_combo" in result["reasons"]
        assert result["keyword_matches"] > 0
        assert result["price_matches"] > 0
    
    def test_price_only(self, detector):
        """Test detection with price patterns only"""
        text = "Great VPS deal at $10/month with 2GB RAM"
        
        result = detector.is_flash_sale(text)
        
        assert result["price_matches"] > 0
        assert result["spec_matches"] > 0
    
    def test_false_positive_reduction(self, detector):
        """Test reduction of false positives"""
        text = "This sale is sold out and expired"
        
        result = detector.is_flash_sale(text)
        
        # Should have reduced confidence due to negative indicators
        assert "negative_indicator" in result["reasons"]
    
    def test_empty_text(self, detector):
        """Test handling of empty or very short text"""
        result = detector.is_flash_sale("")
        assert result["is_flash_sale"] is False
        assert result["confidence"] == 0.0
        
        result = detector.is_flash_sale("hi")
        assert result["is_flash_sale"] is False
    
    def test_keyword_matching(self, detector):
        """Test keyword matching functionality"""
        text = "Limited time offer with special discount pricing"
        
        result = detector.is_flash_sale(text)
        
        assert result["keyword_matches"] >= 3  # offer, limited, discount
        assert "keyword: offer" in result["reasons"]
        assert "keyword: limited" in result["reasons"]
        assert "keyword: discount" in result["reasons"]


class TestAntiDetectionManager:
    """Test cases for AntiDetectionManager"""
    
    @pytest.fixture
    def manager(self):
        """Create anti-detection manager"""
        config = CrawlerConfig()
        return AntiDetectionManager(config)
    
    def test_random_user_agent(self, manager):
        """Test random user agent selection"""
        ua1 = manager.get_random_user_agent()
        ua2 = manager.get_random_user_agent()
        
        assert ua1 in manager.config.user_agents
        assert ua2 in manager.config.user_agents
        # They might be the same, but should be valid
    
    def test_random_delay(self, manager):
        """Test random delay generation"""
        delay = manager.get_random_delay()
        
        assert manager.config.min_request_delay <= delay <= manager.config.max_request_delay
    
    def test_break_logic(self, manager):
        """Test break logic"""
        # Initially should not need break
        assert not manager.should_take_break()
        
        # After 50 requests, should need break
        manager.request_count = 50
        assert manager.should_take_break()
        
        # After 51 requests, should not need break (only every 50)
        manager.request_count = 51
        assert not manager.should_take_break()


class TestTimeUtils:
    """Test cases for TimeUtils"""
    
    def test_parse_iso_format(self):
        """Test parsing ISO format timestamps"""
        iso_time = "2025-07-14T10:30:00+00:00"
        result = TimeUtils.parse_time_string(iso_time)
        
        assert result is not None
        assert result.year == 2025
        assert result.month == 7
        assert result.day == 14
        assert result.tzinfo is not None
    
    def test_parse_iso_with_z(self):
        """Test parsing ISO format with Z suffix"""
        iso_time = "2025-07-14T10:30:00Z"
        result = TimeUtils.parse_time_string(iso_time)
        
        assert result is not None
        assert result.tzinfo is not None
    
    def test_parse_relative_date(self):
        """Test parsing relative date formats"""
        date_str = "July 14"
        result = TimeUtils.parse_time_string(date_str)
        
        assert result is not None
        assert result.month == 7
        assert result.day == 14
    
    def test_parse_none(self):
        """Test handling of None input"""
        result = TimeUtils.parse_time_string(None)
        assert result is None
    
    def test_parse_invalid(self):
        """Test handling of invalid time strings"""
        result = TimeUtils.parse_time_string("invalid time")
        assert result is None


class TestHealthMonitor:
    """Test cases for HealthMonitor"""
    
    @pytest.fixture
    def monitor(self):
        """Create health monitor instance"""
        return HealthMonitor()
    
    def test_update_thread_health(self, monitor):
        """Test updating thread health"""
        monitor.update_thread_health("worker-1", "active", "Processing task")
        
        assert "worker-1" in monitor.thread_health
        assert monitor.thread_health["worker-1"]["status"] == "active"
        assert monitor.thread_health["worker-1"]["details"] == "Processing task"
        assert "worker-1" in monitor.last_activity
    
    def test_check_thread_health(self, monitor):
        """Test health checking functionality"""
        # Add some threads
        monitor.update_thread_health("worker-1", "active", "Working")
        monitor.update_thread_health("worker-2", "error", "Failed")
        
        # Simulate old activity for worker-3
        monitor.last_activity["worker-3"] = time.time() - 400  # 400 seconds ago
        monitor.thread_health["worker-3"] = {"status": "active"}
        
        health_report = monitor.check_thread_health(max_inactive_time=300)
        
        assert "worker-1" in health_report["healthy_threads"]
        assert len(health_report["unhealthy_threads"]) == 1
        assert health_report["unhealthy_threads"][0]["thread_id"] == "worker-2"
        assert len(health_report["inactive_threads"]) == 1
        assert health_report["inactive_threads"][0]["thread_id"] == "worker-3"


class TestForumCrawler:
    """Integration tests for ForumCrawler"""
    
    @pytest.fixture
    def temp_config(self, tmp_path):
        """Create temporary config for testing"""
        config = CrawlerConfig()
        config.state_file = str(tmp_path / "test_state.json")
        config.results_file = str(tmp_path / "test_results.json")
        config.num_workers = 2  # Reduce for testing
        config.headless = True
        return config
    
    def test_crawler_initialization(self, temp_config):
        """Test crawler initialization"""
        crawler = ForumCrawler(temp_config)
        
        assert crawler.config == temp_config
        assert crawler.state_manager is not None
        assert crawler.detector is not None
        assert isinstance(crawler.task_queue, queue.Queue)
        assert isinstance(crawler.all_flash_sales_found, list)
    
    def test_should_process_post_new(self, temp_config):
        """Test post processing logic for new posts"""
        crawler = ForumCrawler(temp_config)
        
        post_data = {
            "post_url": "http://test.com/new-post",
            "post_title": "New Post",
            "current_comment_count": 5,
            "datetime_attr": "2025-07-14T10:00:00Z"
        }
        
        processed_posts = {}  # Empty - no posts processed yet
        
        should_process = crawler._should_process_post(post_data, processed_posts)
        assert should_process is True
    
    def test_should_process_post_updated(self, temp_config):
        """Test post processing logic for updated posts"""
        crawler = ForumCrawler(temp_config)
        
        post_data = {
            "post_url": "http://test.com/existing-post",
            "post_title": "Existing Post",
            "current_comment_count": 10,
            "datetime_attr": "2025-07-14T12:00:00Z"
        }
        
        processed_posts = {
            "http://test.com/existing-post": {
                "last_comment_count": 5,
                "last_comment_datetime": "2025-07-14T10:00:00Z",
                "processed_comment_ids": []
            }
        }
        
        should_process = crawler._should_process_post(post_data, processed_posts)
        assert should_process is True  # Comment count increased
    
    def test_should_process_post_unchanged(self, temp_config):
        """Test post processing logic for unchanged posts"""
        crawler = ForumCrawler(temp_config)
        
        post_data = {
            "post_url": "http://test.com/unchanged-post",
            "post_title": "Unchanged Post",
            "current_comment_count": 5,
            "datetime_attr": "2025-07-14T10:00:00Z"
        }
        
        processed_posts = {
            "http://test.com/unchanged-post": {
                "last_comment_count": 5,
                "last_comment_datetime": "2025-07-14T10:00:00Z",
                "processed_comment_ids": []
            }
        }
        
        should_process = crawler._should_process_post(post_data, processed_posts)
        assert should_process is False  # No changes


@pytest.mark.integration
class TestBrowserManager:
    """Integration tests for BrowserManager (requires actual browser)"""
    
    @pytest.fixture
    def config(self):
        """Create config for browser testing"""
        config = CrawlerConfig()
        config.headless = True  # Use headless for testing
        return config
    
    @pytest.mark.skip(reason="Requires actual browser installation")
    def test_browser_lifecycle(self, config):
        """Test browser start/stop lifecycle"""
        with BrowserManager(config) as browser_manager:
            assert browser_manager.browser is not None
            
            # Test tab creation
            tab = browser_manager.create_tab()
            assert tab is not None
            
            # Test navigation (to a simple page)
            tab.get("data:text/html,<html><body>Test</body></html>")
            
        # Browser should be closed after context exit


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
