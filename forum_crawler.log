2025-07-14 15:22:48,638 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:22:48,638 - forum_crawler - INFO - [MainThread] - Configuration: 5 workers, 60s refresh interval
2025-07-14 15:22:48,639 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:22:48,639 - forum_crawler.browser - ERROR - [MainThread] - Failed to start browser: Chromium.__new__() got an unexpected keyword argument 'headless'
2025-07-14 15:22:48,639 - forum_crawler.main - ERROR - [MainThread] - Critical error in crawler: Chromium.__new__() got an unexpected keyword argument 'headless'
2025-07-14 15:22:48,639 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:22:48,640 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:22:48,640 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:23:08,050 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:23:08,051 - forum_crawler - INFO - [MainThread] - Configuration: 5 workers, 60s refresh interval
2025-07-14 15:23:08,051 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:23:08,051 - forum_crawler.browser - ERROR - [MainThread] - Failed to start browser: Chromium.__new__() got an unexpected keyword argument 'headless'
2025-07-14 15:23:08,051 - forum_crawler.main - ERROR - [MainThread] - Critical error in crawler: Chromium.__new__() got an unexpected keyword argument 'headless'
2025-07-14 15:23:08,052 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:23:08,052 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:23:08,052 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:26:35,345 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:26:35,345 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:26:35,345 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:26:35,352 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:26:35,353 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:26:35,353 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:26:35,732 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:26:35,732 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:26:35,741 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:26:35,742 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:27:23,591 - forum_crawler.main - ERROR - [Monitor] - Error in _process_posts: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:27:23,593 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:27:23,593 - forum_crawler.main - INFO - [Monitor] - Monitor thread ended
2025-07-14 15:27:23,593 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:27:23,593 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:27:23,593 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:27:24,421 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:27:24,421 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:27:24,422 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:27:24,422 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:28:07,263 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:28:07,263 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:28:07,264 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:28:07,903 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:28:07,904 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:28:07,904 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:28:08,132 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:28:08,139 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:28:08,143 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:28:08,143 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:29:07,934 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:29:38,145 - forum_crawler.main - WARNING - [Worker-1] - [Worker-1] Monitor thread not ready, worker exiting
2025-07-14 15:29:38,145 - forum_crawler.main - WARNING - [Worker-2] - [Worker-2] Monitor thread not ready, worker exiting
2025-07-14 15:29:38,145 - forum_crawler.main - WARNING - [Worker-3] - [Worker-3] Monitor thread not ready, worker exiting
2025-07-14 15:29:38,146 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:29:38,146 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:29:38,146 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:30:05,173 - forum_crawler.main - ERROR - [Monitor] - Error in _process_posts: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:30:05,174 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:30:05,174 - forum_crawler.main - INFO - [Monitor] - Monitor thread ended
2025-07-14 15:30:05,981 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:30:05,981 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:30:05,982 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:30:05,982 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:30:13,231 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:30:13,231 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:30:13,232 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:30:13,874 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:30:13,876 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:30:13,876 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:30:14,099 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:30:14,108 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:30:14,109 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:30:14,110 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:31:13,906 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:31:44,118 - forum_crawler.main - WARNING - [Worker-2] - [Worker-2] Monitor thread not ready, worker exiting
2025-07-14 15:31:44,118 - forum_crawler.main - WARNING - [Worker-3] - [Worker-3] Monitor thread not ready, worker exiting
2025-07-14 15:31:44,118 - forum_crawler.main - WARNING - [Worker-1] - [Worker-1] Monitor thread not ready, worker exiting
2025-07-14 15:31:44,118 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:31:44,118 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:31:44,118 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:32:13,935 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:32:38,148 - forum_crawler.main - ERROR - [Monitor] - Error in _process_posts: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:32:38,148 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:32:38,148 - forum_crawler.main - INFO - [Monitor] - Monitor thread ended
2025-07-14 15:32:38,976 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:32:38,976 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:32:38,977 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:32:38,977 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:37:42,543 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:37:42,543 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:37:42,544 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:37:43,191 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:37:43,192 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:37:43,192 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:37:43,409 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:37:43,409 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:37:43,409 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:37:43,410 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:37:48,576 - forum_crawler.main - INFO - [Monitor] - Waiting for page to load...
2025-07-14 15:37:51,577 - forum_crawler.main - INFO - [Monitor] - Trying selector: tag:ul@class=DataList Discussions
2025-07-14 15:37:51,581 - forum_crawler.main - INFO - [Monitor] - Found posts container with selector: tag:ul@class=DataList Discussions
2025-07-14 15:37:51,622 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:38:43,222 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:39:13,421 - forum_crawler.main - WARNING - [Worker-1] - [Worker-1] Monitor thread not ready, worker exiting
2025-07-14 15:39:13,421 - forum_crawler.main - WARNING - [Worker-2] - [Worker-2] Monitor thread not ready, worker exiting
2025-07-14 15:39:13,421 - forum_crawler.main - WARNING - [Worker-3] - [Worker-3] Monitor thread not ready, worker exiting
2025-07-14 15:39:13,421 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:39:13,421 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:39:13,422 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:39:30,466 - forum_crawler.main - INFO - [Monitor] - Initial scan found 5 posts to check
2025-07-14 15:39:30,468 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:39:30,468 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:39:30,468 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:39:30,469 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:39:30,469 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:39:30,470 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:39:30,470 - forum_crawler.main - INFO - [Monitor] - Monitor thread ready event set - workers can now start
2025-07-14 15:39:30,470 - forum_crawler.main - INFO - [Monitor] - Monitor thread ended
2025-07-14 15:39:31,278 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:39:31,278 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:39:31,279 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:39:31,279 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:40:26,181 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:40:26,182 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:40:26,183 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:40:26,821 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:40:26,823 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:40:26,823 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:40:27,047 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:40:27,047 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:40:27,048 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:40:27,055 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:40:34,184 - forum_crawler.main - INFO - [Monitor] - Waiting for page to load...
2025-07-14 15:40:37,185 - forum_crawler.main - INFO - [Monitor] - Trying selector: tag:ul@class=DataList Discussions
2025-07-14 15:40:37,190 - forum_crawler.main - INFO - [Monitor] - Found posts container with selector: tag:ul@class=DataList Discussions
2025-07-14 15:40:37,254 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:41:27,063 - forum_crawler.main - INFO - [Monitor] - Initial scan found 5 posts to check
2025-07-14 15:41:27,063 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:41:27,063 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:41:27,064 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:41:27,064 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:41:27,064 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:41:27,064 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:41:27,065 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:41:27,065 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:41:27,065 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:41:27,065 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:41:27,066 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:41:27,066 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:41:27,066 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:41:27,067 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:41:27,067 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:41:27,067 - forum_crawler.main - ERROR - [Monitor] - Error extracting post data: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:41:27,067 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:41:27,068 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:41:27,068 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Initial scan
2025-07-14 15:41:27,068 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:41:27,069 - forum_crawler.main - INFO - [Monitor] - Monitor thread ready event set - workers can now start
2025-07-14 15:41:27,069 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:41:27,069 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:41:27,069 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:41:27,069 - forum_crawler.main - INFO - [Monitor] - Monitor thread ended
2025-07-14 15:41:27,892 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:41:27,892 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:41:27,893 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:41:27,893 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:44:15,608 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:44:15,609 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:44:15,609 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:44:16,250 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:44:16,251 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:44:16,251 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:44:16,474 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:44:16,474 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:44:16,477 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:44:16,479 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:44:22,770 - forum_crawler.main - INFO - [Monitor] - Waiting for page to load...
2025-07-14 15:44:25,771 - forum_crawler.main - INFO - [Monitor] - Trying selector: tag:ul@class=DataList Discussions
2025-07-14 15:44:25,777 - forum_crawler.main - INFO - [Monitor] - Found posts container with selector: tag:ul@class=DataList Discussions
2025-07-14 15:44:25,821 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:44:25,838 - forum_crawler.main - INFO - [Monitor] - Initial scan found 5 posts to check
2025-07-14 15:44:25,838 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:44:25,839 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:44:25,839 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:44:27,847 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:44:27,848 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:44:29,855 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:44:29,855 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:44:31,864 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:44:31,864 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:44:33,874 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:44:33,874 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:44:35,883 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:44:35,884 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:44:35,884 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Initial scan
2025-07-14 15:44:35,884 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:44:35,885 - forum_crawler.main - INFO - [Monitor] - Monitor thread ready event set - workers can now start
2025-07-14 15:44:38,933 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:44:38,946 - forum_crawler.main - INFO - [Monitor] - Update scan found 5 posts to check
2025-07-14 15:44:38,946 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:44:38,947 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:44:38,947 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:44:40,955 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:44:40,955 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:44:42,964 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:44:42,964 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:44:44,975 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:44:44,975 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:44:47,033 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:44:47,034 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:44:49,042 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:44:49,042 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:44:49,042 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Update scan
2025-07-14 15:45:17,133 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:45:35,904 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:45:35,904 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:45:35,904 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:45:37,183 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:45:37,183 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:45:47,188 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:45:47,188 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
2025-07-14 15:47:10,850 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:47:10,850 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:47:10,851 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:47:11,798 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:47:11,801 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:47:11,802 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:47:12,851 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:47:12,955 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:47:13,023 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:47:13,024 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:47:19,224 - forum_crawler.main - INFO - [Monitor] - Waiting for page to load...
2025-07-14 15:47:22,224 - forum_crawler.main - INFO - [Monitor] - Trying selector: tag:ul@class=DataList Discussions
2025-07-14 15:47:22,229 - forum_crawler.main - INFO - [Monitor] - Found posts container with selector: tag:ul@class=DataList Discussions
2025-07-14 15:47:22,269 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:47:22,282 - forum_crawler.main - INFO - [Monitor] - Initial scan found 5 posts to check
2025-07-14 15:47:22,282 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:47:22,282 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:47:22,282 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:47:24,287 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:47:24,287 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:47:26,290 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:47:26,290 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:47:28,292 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:47:28,292 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:47:30,296 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:47:30,296 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:47:32,297 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:47:32,297 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:47:32,298 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Initial scan
2025-07-14 15:47:32,298 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:47:32,298 - forum_crawler.main - INFO - [Monitor] - Monitor thread ready event set - workers can now start
2025-07-14 15:47:35,350 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:47:35,362 - forum_crawler.main - INFO - [Monitor] - Update scan found 5 posts to check
2025-07-14 15:47:35,363 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:47:35,363 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:47:35,363 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:47:37,369 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:47:37,370 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:47:39,372 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:47:39,372 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:47:41,382 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:47:41,383 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:47:43,390 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:47:43,390 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:47:45,394 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:47:45,394 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:47:45,395 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Update scan
2025-07-14 15:48:12,307 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:48:48,442 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:48:48,456 - forum_crawler.main - INFO - [Monitor] - Update scan found 5 posts to check
2025-07-14 15:48:48,456 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:48:48,456 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:48:48,456 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:48:50,466 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:48:50,466 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:48:52,467 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:48:52,467 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:48:54,476 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:48:54,476 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:48:56,487 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:48:56,487 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:48:58,497 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:48:58,497 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:48:58,497 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Update scan
2025-07-14 15:49:12,339 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:49:38,557 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:49:38,558 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:56:26,947 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:56:26,948 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:56:26,948 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:56:28,005 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:56:28,759 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:56:28,760 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:56:28,878 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:56:28,879 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:56:28,879 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:56:28,879 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:56:35,151 - forum_crawler.main - INFO - [Monitor] - Waiting for page to load...
2025-07-14 15:56:38,152 - forum_crawler.main - INFO - [Monitor] - Trying selector: tag:ul@class=DataList Discussions
2025-07-14 15:56:38,159 - forum_crawler.main - INFO - [Monitor] - Found posts container with selector: tag:ul@class=DataList Discussions
2025-07-14 15:56:38,201 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:56:38,215 - forum_crawler.main - INFO - [Monitor] - Initial scan found 5 posts to check
2025-07-14 15:56:38,215 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:56:38,215 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:56:38,215 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:56:40,224 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:56:40,224 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:56:42,227 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:56:42,227 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:56:44,237 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:56:44,237 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:56:46,246 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:56:46,246 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:56:48,249 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 5
2025-07-14 15:56:48,249 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:56:48,250 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Initial scan
2025-07-14 15:56:48,250 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:56:48,250 - forum_crawler.main - INFO - [Monitor] - Monitor thread ready event set - workers can now start
2025-07-14 15:56:51,312 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:56:51,326 - forum_crawler.main - INFO - [Monitor] - Update scan found 5 posts to check
2025-07-14 15:56:51,326 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:56:51,326 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:56:51,326 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:56:53,329 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 1
2025-07-14 15:56:53,329 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:56:55,331 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 2
2025-07-14 15:56:55,331 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:56:57,340 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 3
2025-07-14 15:56:57,340 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:56:59,342 - forum_crawler.main - WARNING - [Monitor] - Could not extract data from post 4
2025-07-14 15:56:59,342 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:58:17,207 - forum_crawler - INFO - [MainThread] - Starting Improved Forum Crawler
2025-07-14 15:58:17,207 - forum_crawler - INFO - [MainThread] - Configuration: 3 workers, 60s refresh interval
2025-07-14 15:58:17,208 - forum_crawler.main - INFO - [MainThread] - Starting Forum Crawler...
2025-07-14 15:58:17,846 - forum_crawler.browser - INFO - [MainThread] - Browser started successfully
2025-07-14 15:58:17,847 - forum_crawler.main - INFO - [MainThread] - Started 3 worker threads and 1 monitor thread
2025-07-14 15:58:17,847 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:58:18,064 - forum_crawler.main - INFO - [Monitor] - Loading forum page: https://lowendtalk.com/categories/offers
2025-07-14 15:58:18,087 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Waiting for monitor thread...
2025-07-14 15:58:18,109 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Waiting for monitor thread...
2025-07-14 15:58:18,109 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Waiting for monitor thread...
2025-07-14 15:58:24,603 - forum_crawler.main - INFO - [Monitor] - Waiting for page to load...
2025-07-14 15:58:27,604 - forum_crawler.main - INFO - [Monitor] - Trying selector: tag:ul@class=DataList Discussions
2025-07-14 15:58:27,610 - forum_crawler.main - INFO - [Monitor] - Found posts container with selector: tag:ul@class=DataList Discussions
2025-07-14 15:58:27,657 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:58:27,672 - forum_crawler.main - INFO - [Monitor] - Initial scan found 5 posts to check
2025-07-14 15:58:27,673 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:58:27,673 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:58:27,673 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:58:27,703 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:58:27,712 - forum_crawler.main - INFO - [Monitor] - Extracted: Don��t Miss Out �C HostDare VPS Sale!... (Comments: 1700)
2025-07-14 15:58:27,712 - forum_crawler.main - INFO - [Monitor] - Extracted post: Don��t Miss Out �C HostDare VPS Sale!...
2025-07-14 15:58:27,712 - forum_crawler.main - INFO - [Monitor] - Queued post: Don��t Miss Out �C HostDare VPS Sale!...
2025-07-14 15:58:27,712 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:58:27,725 - forum_crawler.main - INFO - [Monitor] - Extracted: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ... (Comments: 1400)
2025-07-14 15:58:27,725 - forum_crawler.main - INFO - [Monitor] - Extracted post: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ...
2025-07-14 15:58:27,726 - forum_crawler.main - INFO - [Monitor] - Queued post: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ...
2025-07-14 15:58:27,726 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:58:27,735 - forum_crawler.main - INFO - [Monitor] - Extracted: fiberstate... (Comments: 22500)
2025-07-14 15:58:27,735 - forum_crawler.main - INFO - [Monitor] - Extracted post: fiberstate...
2025-07-14 15:58:27,736 - forum_crawler.main - INFO - [Monitor] - Queued post: fiberstate...
2025-07-14 15:58:27,736 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:58:27,743 - forum_crawler.main - INFO - [Monitor] - Extracted: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB... (Comments: 15900)
2025-07-14 15:58:27,744 - forum_crawler.main - INFO - [Monitor] - Extracted post: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB...
2025-07-14 15:58:27,744 - forum_crawler.main - INFO - [Monitor] - Queued post: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB...
2025-07-14 15:58:27,744 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:58:27,745 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Initial scan
2025-07-14 15:58:27,745 - forum_crawler.main - INFO - [Monitor] - Initial scan completed, monitor thread ready
2025-07-14 15:58:27,745 - forum_crawler.main - INFO - [Monitor] - Monitor thread ready event set - workers can now start
2025-07-14 15:58:27,745 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Processing: Don��t Miss Out �C HostDare VPS Sale!
2025-07-14 15:58:27,745 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Processing: HostUS - High Performance Ryzen KVM VPS - 1GB KVM from $25/year ($2/mo) - Mount your own ISO!
2025-07-14 15:58:31,001 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:58:31,026 - forum_crawler.main - INFO - [Monitor] - Update scan found 5 posts to check
2025-07-14 15:58:31,027 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:58:31,028 - forum_crawler.main - INFO - [Monitor] - Loaded state with 3 previously processed posts
2025-07-14 15:58:31,029 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:58:31,049 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:58:31,069 - forum_crawler.main - INFO - [Monitor] - Extracted: Don��t Miss Out �C HostDare VPS Sale!... (Comments: 1700)
2025-07-14 15:58:31,069 - forum_crawler.main - INFO - [Monitor] - Extracted post: Don��t Miss Out �C HostDare VPS Sale!...
2025-07-14 15:58:31,069 - forum_crawler.main - INFO - [Monitor] - Queued post: Don��t Miss Out �C HostDare VPS Sale!...
2025-07-14 15:58:31,069 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:58:31,080 - forum_crawler.main - INFO - [Monitor] - Extracted: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ... (Comments: 1400)
2025-07-14 15:58:31,080 - forum_crawler.main - INFO - [Monitor] - Extracted post: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ...
2025-07-14 15:58:31,080 - forum_crawler.main - INFO - [Monitor] - Queued post: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ...
2025-07-14 15:58:31,081 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:58:31,105 - forum_crawler.main - INFO - [Monitor] - Extracted: fiberstate... (Comments: 22500)
2025-07-14 15:58:31,105 - forum_crawler.main - INFO - [Monitor] - Extracted post: fiberstate...
2025-07-14 15:58:31,105 - forum_crawler.main - INFO - [Monitor] - Queued post: fiberstate...
2025-07-14 15:58:31,106 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:58:31,116 - forum_crawler.main - INFO - [Monitor] - Extracted: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB... (Comments: 15900)
2025-07-14 15:58:31,117 - forum_crawler.main - INFO - [Monitor] - Extracted post: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB...
2025-07-14 15:58:31,119 - forum_crawler.main - INFO - [Monitor] - Queued post: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB...
2025-07-14 15:58:31,119 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:58:31,120 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Update scan
2025-07-14 15:58:54,822 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Found 15 comments
2025-07-14 15:58:54,826 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Processing: fiberstate
2025-07-14 15:59:00,943 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Found 30 comments
2025-07-14 15:59:06,214 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Found 19 comments
2025-07-14 15:59:18,016 - forum_crawler.health - INFO - [MainThread] - Health Summary - Healthy: 4, Unhealthy: 0, Inactive: 0
2025-07-14 15:59:21,249 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Found 30 comments
2025-07-14 15:59:21,255 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Processing: Don��t Miss Out �C HostDare VPS Sale!
2025-07-14 15:59:24,082 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Found 0 comments
2025-07-14 15:59:24,083 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Processing: HostUS - High Performance Ryzen KVM VPS - 1GB KVM from $25/year ($2/mo) - Mount your own ISO!
2025-07-14 15:59:25,493 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Found 30 comments
2025-07-14 15:59:25,503 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Processing: fiberstate
2025-07-14 15:59:34,620 - forum_crawler.main - INFO - [Monitor] - Found 75 items using selector: xpath:./li[contains(@class, "Item")]
2025-07-14 15:59:34,636 - forum_crawler.main - INFO - [Monitor] - Update scan found 5 posts to check
2025-07-14 15:59:34,636 - forum_crawler.main - INFO - [Monitor] - Loading current state...
2025-07-14 15:59:34,636 - forum_crawler.main - INFO - [Monitor] - Loaded state with 8 previously processed posts
2025-07-14 15:59:34,636 - forum_crawler.main - INFO - [Monitor] - Processing post 1/5
2025-07-14 15:59:34,648 - forum_crawler.main - INFO - [Monitor] - Processing post 2/5
2025-07-14 15:59:34,663 - forum_crawler.main - INFO - [Monitor] - Extracted: Don��t Miss Out �C HostDare VPS Sale!... (Comments: 1700)
2025-07-14 15:59:34,663 - forum_crawler.main - INFO - [Monitor] - Extracted post: Don��t Miss Out �C HostDare VPS Sale!...
2025-07-14 15:59:34,663 - forum_crawler.main - INFO - [Monitor] - Processing post 3/5
2025-07-14 15:59:34,673 - forum_crawler.main - INFO - [Monitor] - Extracted: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ... (Comments: 1400)
2025-07-14 15:59:34,673 - forum_crawler.main - INFO - [Monitor] - Extracted post: HostUS - High Performance Ryzen KVM VPS - 1GB KVM ...
2025-07-14 15:59:34,673 - forum_crawler.main - INFO - [Monitor] - Processing post 4/5
2025-07-14 15:59:34,680 - forum_crawler.main - INFO - [Monitor] - Extracted: fiberstate... (Comments: 22500)
2025-07-14 15:59:34,681 - forum_crawler.main - INFO - [Monitor] - Extracted post: fiberstate...
2025-07-14 15:59:34,681 - forum_crawler.main - INFO - [Monitor] - Processing post 5/5
2025-07-14 15:59:34,693 - forum_crawler.main - INFO - [Monitor] - Extracted: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB... (Comments: 15900)
2025-07-14 15:59:34,693 - forum_crawler.main - INFO - [Monitor] - Extracted post: Stock Clearance - 1Gbit, 10Gbit with 4x3TB, 4x16TB...
2025-07-14 15:59:34,693 - forum_crawler.main - INFO - [Monitor] - Saving updated state...
2025-07-14 15:59:34,693 - forum_crawler.main - INFO - [Monitor] - Post processing completed for Update scan
2025-07-14 15:59:40,558 - forum_crawler.browser - INFO - [MainThread] - Browser stopped successfully
2025-07-14 15:59:40,558 - forum_crawler.main - INFO - [MainThread] - Starting cleanup process...
2025-07-14 15:59:40,954 - forum_crawler.main - ERROR - [Worker-1] - [Worker-1] Error during scrolling: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:40,966 - forum_crawler.main - ERROR - [Worker-1] - [Worker-1] Error processing post comments: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:40,966 - forum_crawler.main - ERROR - [Worker-1] - [Worker-1] Error processing task: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:40,967 - forum_crawler.main - ERROR - [Worker-1] - [Worker-1] Failed to recover: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:40,967 - forum_crawler.main - INFO - [Worker-1] - [Worker-1] Worker thread ended
2025-07-14 15:59:44,773 - forum_crawler.main - ERROR - [Worker-2] - [Worker-2] Error during scrolling: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:44,783 - forum_crawler.main - ERROR - [Worker-2] - [Worker-2] Error processing post comments: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:44,784 - forum_crawler.main - ERROR - [Worker-2] - [Worker-2] Error processing task: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:44,784 - forum_crawler.main - ERROR - [Worker-2] - [Worker-2] Failed to recover: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:44,785 - forum_crawler.main - INFO - [Worker-2] - [Worker-2] Worker thread ended
2025-07-14 15:59:52,117 - forum_crawler.main - ERROR - [Worker-3] - [Worker-3] Error during scrolling: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:52,128 - forum_crawler.main - ERROR - [Worker-3] - [Worker-3] Error processing post comments: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:52,128 - forum_crawler.main - ERROR - [Worker-3] - [Worker-3] Error processing task: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:52,129 - forum_crawler.main - ERROR - [Worker-3] - [Worker-3] Failed to recover: 
��ҳ��������ѶϿ���
�汾: 4.1.0.18
2025-07-14 15:59:52,129 - forum_crawler.main - INFO - [Worker-3] - [Worker-3] Worker thread ended
2025-07-14 15:59:52,130 - forum_crawler.main - INFO - [MainThread] - Cleanup completed
2025-07-14 15:59:52,130 - forum_crawler - INFO - [MainThread] - Forum crawler shutdown complete
