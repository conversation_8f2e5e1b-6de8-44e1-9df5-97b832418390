import re
from datetime import datetime

def convert_temu_data_to_markdown_for_dingtalk(data_string, date_str="2025-07-08", total_overall_orders=271, max_display_name_length=12): # Reduced default length
    # Split the data into lines and skip the header
    lines = data_string.strip().split('\n')[1:]

    # Initialize a dictionary to hold processed data by region
    processed_data = {
        "US": [],
        "EU": [],
        "Mexico": [],
        "Australia": [],
        "Canada": []
    }

    # Process each line
    for line in lines:
        parts = line.split('\t')
        account = parts[0]
        raw_store_name = parts[1]

        # Clean up store name: take only the part before the first space
        # This means "Walking Electronic" becomes "Walking"
        clean_store_name = raw_store_name.split(' ')[0]

        us_orders = parts[2] if len(parts) > 2 and parts[2].strip() else '0'
        eu_orders = parts[3] if len(parts) > 3 and parts[3].strip() else '0'
        mexico_orders = parts[4] if len(parts) > 4 and parts[4].strip() else '0'
        australia_orders = parts[5] if len(parts) > 5 and parts[5].strip() else '0'
        canada_orders = parts[6] if len(parts) > 6 and parts[6].strip() else '0'

        # Extract the relevant part of the account name (e.g., "八玖" from "Temu-八玖")
        company_match = re.match(r'Temu-(.+)', account)
        company_prefix = company_match.group(1) if company_match else account.split('-')[0] # Fallback

        # Combine company_prefix and clean_store_name
        # Special handling for YASEWANGKEJIGONGSI as it repeats
        if company_prefix == "YASEWANGKEJIGONGSI":
            raw_final_shop_display_name = "YASEWANGKEJIGONGSI-YASEWANGKEJIGONGSI"
        else:
            raw_final_shop_display_name = f"{company_prefix}-{clean_store_name}"

        # Apply truncation to ensure single line for DingTalk
        # This is the crucial part for controlling visual width
        if len(raw_final_shop_display_name) > max_display_name_length:
            final_shop_display_name = raw_final_shop_display_name[:max_display_name_length-1] + "…" # Use ellipsis character
        else:
            final_shop_display_name = raw_final_shop_display_name


        # Append data to respective regions if orders exist and are not '0'
        if int(us_orders) > 0:
            processed_data["US"].append((final_shop_display_name, int(us_orders)))
        if int(eu_orders) > 0:
            processed_data["EU"].append((final_shop_display_name, int(eu_orders)))
        if int(mexico_orders) > 0:
            processed_data["Mexico"].append((final_shop_display_name, int(mexico_orders)))
        if int(australia_orders) > 0:
            processed_data["Australia"].append((final_shop_display_name, int(australia_orders)))
        if int(canada_orders) > 0:
            processed_data["Canada"].append((final_shop_display_name, int(canada_orders)))

    # Calculate regional totals
    region_totals = {region: sum(item[1] for item in data) for region, data in processed_data.items()}

    # Build the Markdown table
    markdown_output = f"{date_str}-Temu总计{total_overall_orders}单\n\n"
    markdown_output += "| 地区 | 地区总单量 | 店铺 | 单量 |\n"
    markdown_output += "| :------------------- | :----------- | :---------------------------------- | :----- |\n" # Adjusted separator for potential width

    region_display_order = ["US", "EU", "Mexico", "Australia", "Canada"]
    region_name_map = {
        "US": "美区",
        "EU": "欧区",
        "Mexico": "墨西哥",
        "Australia": "澳洲",
        "Canada": "加拿大"
    }

    for region_key in region_display_order:
        items = processed_data[region_key]
        total_for_region = region_totals.get(region_key, 0)

        if items:
            items.sort(key=lambda x: x[1], reverse=True)
            first_item_in_region = True
            for shop_name_for_display, orders in items:
                if first_item_in_region:
                    markdown_output += f"| **{region_name_map[region_key]}** | {total_for_region} | {shop_name_for_display} | {orders} |\n"
                    first_item_in_region = False
                else:
                    markdown_output += f"| | | {shop_name_for_display} | {orders} |\n"
        else:
            markdown_output += f"| **{region_name_map[region_key]}** | 0 | | |\n"

    markdown_output += f"| **Grand Total** | **{total_overall_orders}** | | |\n"

    return markdown_output

# Your provided data as a multi-line string
data_source = """
账号	店铺	美国	欧区	墨西哥	全球（澳洲）	全球（加拿大）
Temu-星绮丽	InnoMobile	0			10
YASEWANGKEJIGONGSI	YASEWANGKEJIGONGSI			4
Temu-慕风	Wind showers	0
Temu-慕风	Vanguard Tech	0			27
Temu-慕风	FutureByte		9
Temu-八玖	Walking Electronic	2
Temu-八玖	Freedom Electronic	0
Temu-八玖	SkyMatrix		3
Temu-鼓风	Tech Epoch		0
Temu-燊火	Digital Wave		6
Temu-燊火	SHERON		3			9
Temu-新智	Thinking Electronic	0	90
Temu-新智	Extreme Electronic	0	11		6
Temu-新智	MobileCore		20
Temu-亦谦	TropicTech		4
Temu-研翔	SoulCollide					21
Temu-博青	Smiling Electronic	0	0		14
Temu-博青	Blue Sea Electronic	0	32
"""

# Convert the data and print the Markdown output
# YOU MUST EXPERIMENT WITH 'max_display_name_length' HERE!
# Start with a small number (e.g., 8 or 10) and increase it until it wraps in DingTalk.
# Then choose the largest number that still keeps it on one line.
print(convert_temu_data_to_markdown_for_dingtalk(data_source, date_str="2025-07-08", total_overall_orders=271, max_display_name_length=12))